<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Phase 2 高级功能测试</title>
    <script type="text/javascript" src="https://api.map.baidu.com/api?v=3.0&ak=YOUR_API_KEY"></script>
    <script type="text/javascript" src="https://api.map.baidu.com/library/DrawingManager/1.4/src/DrawingManager_min.js"></script>
    <link rel="stylesheet" href="https://api.map.baidu.com/library/DrawingManager/1.4/src/DrawingManager_min.css" />
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f5f5;
        }
        
        .container {
            display: flex;
            height: 100vh;
        }
        
        .sidebar {
            width: 350px;
            background: white;
            border-right: 1px solid #e0e0e0;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }
        
        .sidebar-header {
            padding: 20px;
            border-bottom: 1px solid #e0e0e0;
            background: #fafafa;
        }
        
        .sidebar-header h1 {
            font-size: 18px;
            color: #333;
            margin-bottom: 8px;
        }
        
        .sidebar-content {
            flex: 1;
            overflow-y: auto;
            padding: 20px;
        }
        
        .test-section {
            margin-bottom: 20px;
            padding: 15px;
            border: 1px solid #e0e0e0;
            border-radius: 4px;
            background: #fafafa;
        }
        
        .test-section h3 {
            font-size: 14px;
            color: #333;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
        }
        
        .test-button {
            display: block;
            width: 100%;
            padding: 8px 12px;
            margin-bottom: 8px;
            background: #1890ff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
        }
        
        .test-button:hover {
            background: #40a9ff;
        }
        
        .test-button:disabled {
            background: #d9d9d9;
            cursor: not-allowed;
        }
        
        .test-button.success {
            background: #52c41a;
        }
        
        .test-button.error {
            background: #ff4d4f;
        }
        
        .map-container {
            flex: 1;
            position: relative;
        }
        
        #mapDiv {
            width: 100%;
            height: 100%;
        }
        
        .status-bar {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 40px;
            background: rgba(255, 255, 255, 0.95);
            border-top: 1px solid #e0e0e0;
            display: flex;
            align-items: center;
            padding: 0 16px;
            font-size: 12px;
            color: #666;
        }
        
        .log-area {
            position: absolute;
            top: 20px;
            right: 20px;
            width: 350px;
            height: 250px;
            background: rgba(0, 0, 0, 0.85);
            color: white;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 11px;
            overflow-y: auto;
            z-index: 1000;
        }
        
        .progress-bar {
            width: 100%;
            height: 4px;
            background: #f0f0f0;
            border-radius: 2px;
            margin: 10px 0;
            overflow: hidden;
        }
        
        .progress-fill {
            height: 100%;
            background: #1890ff;
            width: 0%;
            transition: width 0.3s ease;
        }
        
        .stats-panel {
            position: absolute;
            top: 20px;
            left: 20px;
            width: 250px;
            background: rgba(255, 255, 255, 0.95);
            border: 1px solid #e0e0e0;
            border-radius: 4px;
            padding: 15px;
            font-size: 12px;
            z-index: 1000;
        }
        
        .stats-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
        }
        
        .stats-item:last-child {
            margin-bottom: 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 测试控制面板 -->
        <div class="sidebar">
            <div class="sidebar-header">
                <h1>🚀 Phase 2 高级功能测试</h1>
                <p>图层系统、数据源、绘图工具</p>
                <div class="progress-bar">
                    <div class="progress-fill" id="progressFill"></div>
                </div>
            </div>
            
            <div class="sidebar-content">
                <!-- 基础初始化 -->
                <div class="test-section">
                    <h3>🏗️ 基础初始化</h3>
                    <button class="test-button" onclick="testMapInit()">初始化地图</button>
                    <button class="test-button" onclick="testAdapterInit()">初始化适配器</button>
                </div>
                
                <!-- 图层系统测试 -->
                <div class="test-section">
                    <h3>📋 图层系统测试</h3>
                    <button class="test-button" onclick="testLayerSystemInit()">初始化图层系统</button>
                    <button class="test-button" onclick="testAddLayer()">添加图层</button>
                    <button class="test-button" onclick="testLayerVisibility()">切换图层可见性</button>
                    <button class="test-button" onclick="testLayerOpacity()">调整图层透明度</button>
                    <button class="test-button" onclick="testLayerStatistics()">获取图层统计</button>
                </div>
                
                <!-- 数据源系统测试 -->
                <div class="test-section">
                    <h3>💾 数据源系统测试</h3>
                    <button class="test-button" onclick="testDataSourceInit()">初始化数据源系统</button>
                    <button class="test-button" onclick="testAddDataSource()">添加数据源</button>
                    <button class="test-button" onclick="testLoadData()">加载数据</button>
                    <button class="test-button" onclick="testRefreshDataSource()">刷新数据源</button>
                    <button class="test-button" onclick="testDataSourceStats()">数据源统计</button>
                </div>
                
                <!-- 绘图工具测试 -->
                <div class="test-section">
                    <h3>✏️ 绘图工具测试</h3>
                    <button class="test-button" onclick="testDrawingToolInit()">初始化绘图工具</button>
                    <button class="test-button" onclick="testOpenDrawing()">开启绘制模式</button>
                    <button class="test-button" onclick="testDrawingStyles()">设置绘制样式</button>
                    <button class="test-button" onclick="testUndoRedo()">撤销重做功能</button>
                    <button class="test-button" onclick="testMeasurement()">测量功能</button>
                    <button class="test-button" onclick="testExportImport()">导出导入功能</button>
                </div>
                
                <!-- 集成测试 -->
                <div class="test-section">
                    <h3>🔗 集成测试</h3>
                    <button class="test-button" onclick="testIntegration()">完整集成测试</button>
                    <button class="test-button" onclick="testPerformance()">性能测试</button>
                    <button class="test-button" onclick="testErrorHandling()">错误处理测试</button>
                </div>
                
                <!-- 清理操作 -->
                <div class="test-section">
                    <h3>🧹 清理操作</h3>
                    <button class="test-button" onclick="testClearAll()">清空所有内容</button>
                    <button class="test-button" onclick="testDestroy()">销毁所有实例</button>
                </div>
            </div>
        </div>
        
        <!-- 地图区域 -->
        <div class="map-container">
            <div id="mapDiv"></div>
            
            <!-- 统计面板 -->
            <div class="stats-panel" id="statsPanel">
                <h4 style="margin-bottom: 10px;">📊 实时统计</h4>
                <div class="stats-item">
                    <span>测试进度:</span>
                    <span id="testProgress">0/0</span>
                </div>
                <div class="stats-item">
                    <span>成功测试:</span>
                    <span id="successCount">0</span>
                </div>
                <div class="stats-item">
                    <span>失败测试:</span>
                    <span id="errorCount">0</span>
                </div>
                <div class="stats-item">
                    <span>图层数量:</span>
                    <span id="layerCount">0</span>
                </div>
                <div class="stats-item">
                    <span>数据源数量:</span>
                    <span id="dataSourceCount">0</span>
                </div>
                <div class="stats-item">
                    <span>覆盖物数量:</span>
                    <span id="overlayCount">0</span>
                </div>
            </div>
            
            <!-- 状态栏 -->
            <div class="status-bar">
                <span id="statusText">Phase 2 高级功能测试 - 准备就绪</span>
            </div>
            
            <!-- 日志区域 -->
            <div class="log-area" id="logArea"></div>
        </div>
    </div>

    <script type="module">
        // 导入适配器
        import { 
            MapAdapter, 
            LayerSystemAdapter, 
            DataSourceAdapter, 
            DrawingToolAdapter,
            AdapterUtils 
        } from './src/adapters/index.js';

        // 全局变量
        let map = null;
        let layerSystem = null;
        let dataSourceSystem = null;
        let drawingTool = null;
        
        let testStats = {
            total: 0,
            success: 0,
            error: 0,
            layers: 0,
            dataSources: 0,
            overlays: 0
        };

        // 日志函数
        function log(message, type = 'info') {
            const logArea = document.getElementById('logArea');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.style.color = type === 'error' ? '#ff4d4f' : type === 'success' ? '#52c41a' : '#ffffff';
            logEntry.textContent = `[${timestamp}] ${message}`;
            logArea.appendChild(logEntry);
            logArea.scrollTop = logArea.scrollHeight;
            
            // 更新状态栏
            document.getElementById('statusText').textContent = message;
            
            // 更新统计
            if (type === 'success') {
                testStats.success++;
            } else if (type === 'error') {
                testStats.error++;
            }
            updateStats();
        }

        // 更新统计信息
        function updateStats() {
            document.getElementById('testProgress').textContent = `${testStats.success + testStats.error}/${testStats.total}`;
            document.getElementById('successCount').textContent = testStats.success;
            document.getElementById('errorCount').textContent = testStats.error;
            document.getElementById('layerCount').textContent = testStats.layers;
            document.getElementById('dataSourceCount').textContent = testStats.dataSources;
            document.getElementById('overlayCount').textContent = testStats.overlays;
            
            // 更新进度条
            const progress = testStats.total > 0 ? ((testStats.success + testStats.error) / testStats.total) * 100 : 0;
            document.getElementById('progressFill').style.width = `${progress}%`;
        }

        // 更新按钮状态
        function updateButtonStatus(buttonText, status) {
            const buttons = document.querySelectorAll('.test-button');
            buttons.forEach(button => {
                if (button.textContent === buttonText) {
                    button.className = `test-button ${status}`;
                }
            });
        }

        // 绘制回调函数
        function onDrawComplete(type, data) {
            log(`绘制完成: ${type}`, 'success');
            testStats.overlays++;
            updateStats();
        }

        // 测试函数
        window.testMapInit = async function() {
            testStats.total++;
            try {
                if (typeof BMap === 'undefined') {
                    throw new Error('百度地图API未加载');
                }

                map = new MapAdapter('mapDiv', onDrawComplete, {
                    enablePerformanceMonitoring: true,
                    enableCaching: true,
                    enableErrorHandling: true
                });

                map.setCenter({ lng: 116.404, lat: 39.915 });
                map.setZoom(11);

                log('地图初始化成功', 'success');
                updateButtonStatus('初始化地图', 'success');
            } catch (error) {
                log(`地图初始化失败: ${error.message}`, 'error');
                updateButtonStatus('初始化地图', 'error');
            }
        };

        window.testAdapterInit = async function() {
            testStats.total++;
            try {
                if (!map) {
                    throw new Error('请先初始化地图');
                }

                // 初始化图层系统适配器
                layerSystem = new LayerSystemAdapter(map);
                await layerSystem.init({});

                // 初始化数据源系统适配器
                dataSourceSystem = new DataSourceAdapter();
                await dataSourceSystem.init({});

                // 绘图工具已在地图中初始化
                drawingTool = map.drawingTool;

                log('所有适配器初始化成功', 'success');
                updateButtonStatus('初始化适配器', 'success');
            } catch (error) {
                log(`适配器初始化失败: ${error.message}`, 'error');
                updateButtonStatus('初始化适配器', 'error');
            }
        };

        // 图层系统测试
        window.testLayerSystemInit = async function() {
            testStats.total++;
            try {
                if (!layerSystem) {
                    throw new Error('请先初始化适配器');
                }

                const isReady = layerSystem.isReady();
                if (!isReady) {
                    throw new Error('图层系统未就绪');
                }

                log('图层系统初始化验证成功', 'success');
                updateButtonStatus('初始化图层系统', 'success');
            } catch (error) {
                log(`图层系统初始化验证失败: ${error.message}`, 'error');
                updateButtonStatus('初始化图层系统', 'error');
            }
        };

        window.testAddLayer = async function() {
            testStats.total++;
            try {
                if (!layerSystem) {
                    throw new Error('请先初始化图层系统');
                }

                const layerConfig = {
                    id: `test-layer-${Date.now()}`,
                    name: '测试图层',
                    type: 'marker',
                    visible: true,
                    opacity: 1,
                    data: {
                        features: [
                            {
                                type: 'Feature',
                                geometry: {
                                    type: 'Point',
                                    coordinates: [116.404, 39.915]
                                },
                                properties: {
                                    name: '测试点'
                                }
                            }
                        ]
                    }
                };

                await layerSystem.addLayer(layerConfig);
                testStats.layers++;

                log(`添加图层成功: ${layerConfig.name}`, 'success');
                updateButtonStatus('添加图层', 'success');
            } catch (error) {
                log(`添加图层失败: ${error.message}`, 'error');
                updateButtonStatus('添加图层', 'error');
            }
        };

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            log('Phase 2 高级功能测试页面已加载', 'info');
            log('请按顺序执行测试：初始化地图 → 初始化适配器 → 其他测试', 'info');
            
            // 设置总测试数量
            testStats.total = 0;
            updateStats();
        });

        // 其他测试函数将在下一部分添加...
    </script>
</body>
</html>
