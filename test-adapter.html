<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>适配器测试页面</title>
    <script type="text/javascript" src="https://api.map.baidu.com/api?v=3.0&ak=YOUR_API_KEY"></script>
    <script type="text/javascript" src="https://api.map.baidu.com/library/DrawingManager/1.4/src/DrawingManager_min.js"></script>
    <link rel="stylesheet" href="https://api.map.baidu.com/library/DrawingManager/1.4/src/DrawingManager_min.css" />
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f5f5;
        }
        
        .container {
            display: flex;
            height: 100vh;
        }
        
        .sidebar {
            width: 300px;
            background: white;
            border-right: 1px solid #e0e0e0;
            padding: 20px;
            overflow-y: auto;
        }
        
        .sidebar h1 {
            font-size: 18px;
            color: #333;
            margin-bottom: 20px;
        }
        
        .test-section {
            margin-bottom: 20px;
            padding: 15px;
            border: 1px solid #e0e0e0;
            border-radius: 4px;
            background: #fafafa;
        }
        
        .test-section h3 {
            font-size: 14px;
            color: #333;
            margin-bottom: 10px;
        }
        
        .test-button {
            display: block;
            width: 100%;
            padding: 8px 12px;
            margin-bottom: 8px;
            background: #1890ff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
        }
        
        .test-button:hover {
            background: #40a9ff;
        }
        
        .test-button:disabled {
            background: #d9d9d9;
            cursor: not-allowed;
        }
        
        .map-container {
            flex: 1;
            position: relative;
        }
        
        #mapDiv {
            width: 100%;
            height: 100%;
        }
        
        .status-bar {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 40px;
            background: rgba(255, 255, 255, 0.95);
            border-top: 1px solid #e0e0e0;
            display: flex;
            align-items: center;
            padding: 0 16px;
            font-size: 12px;
            color: #666;
        }
        
        .log-area {
            position: absolute;
            top: 20px;
            right: 20px;
            width: 300px;
            height: 200px;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 11px;
            overflow-y: auto;
            z-index: 1000;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 测试控制面板 -->
        <div class="sidebar">
            <h1>🧪 适配器测试</h1>
            
            <!-- 基础地图测试 -->
            <div class="test-section">
                <h3>🗺️ 基础地图测试</h3>
                <button class="test-button" onclick="testMapInit()">初始化地图</button>
                <button class="test-button" onclick="testMapCenter()">设置地图中心</button>
                <button class="test-button" onclick="testMapZoom()">设置缩放级别</button>
                <button class="test-button" onclick="testMapDrag()">切换拖拽状态</button>
            </div>
            
            <!-- 覆盖物测试 -->
            <div class="test-section">
                <h3>📍 覆盖物测试</h3>
                <button class="test-button" onclick="testAddMarker()">添加标记点</button>
                <button class="test-button" onclick="testAddPolygon()">添加多边形</button>
                <button class="test-button" onclick="testAddPolyline()">添加折线</button>
                <button class="test-button" onclick="testClearOverlays()">清空覆盖物</button>
            </div>
            
            <!-- 信息窗口测试 -->
            <div class="test-section">
                <h3>💬 信息窗口测试</h3>
                <button class="test-button" onclick="testOpenInfoWindow()">打开信息窗口</button>
                <button class="test-button" onclick="testCloseInfoWindow()">关闭信息窗口</button>
            </div>
            
            <!-- 省份多边形测试 -->
            <div class="test-section">
                <h3>🏛️ 省份多边形测试</h3>
                <button class="test-button" onclick="testMarkProvince()">标记省份</button>
                <button class="test-button" onclick="testClearProvince()">清除省份</button>
            </div>
            
            <!-- 性能测试 -->
            <div class="test-section">
                <h3>⚡ 性能测试</h3>
                <button class="test-button" onclick="testPerformanceReport()">获取性能报告</button>
                <button class="test-button" onclick="testOptimizePerformance()">优化性能</button>
            </div>
            
            <!-- 适配器测试 -->
            <div class="test-section">
                <h3>🔧 适配器测试</h3>
                <button class="test-button" onclick="testAdapterCompatibility()">兼容性检查</button>
                <button class="test-button" onclick="testAdapterVersion()">版本信息</button>
                <button class="test-button" onclick="testLibInstance()">获取lib实例</button>
            </div>
        </div>
        
        <!-- 地图区域 -->
        <div class="map-container">
            <div id="mapDiv"></div>
            
            <!-- 状态栏 -->
            <div class="status-bar">
                <span id="statusText">等待初始化...</span>
            </div>
            
            <!-- 日志区域 -->
            <div class="log-area" id="logArea"></div>
        </div>
    </div>

    <script type="module">
        // 导入适配器
        import { MapAdapter, MarkerAdapter, PolygonAdapter, PolylineAdapter, AdapterUtils } from './src/adapters/index.js';

        let map = null;
        let overlays = [];

        // 日志函数
        function log(message, type = 'info') {
            const logArea = document.getElementById('logArea');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.style.color = type === 'error' ? '#ff4d4f' : type === 'success' ? '#52c41a' : '#ffffff';
            logEntry.textContent = `[${timestamp}] ${message}`;
            logArea.appendChild(logEntry);
            logArea.scrollTop = logArea.scrollHeight;
            
            // 更新状态栏
            document.getElementById('statusText').textContent = message;
        }

        // 绘制回调函数
        function onDrawComplete(type, data) {
            log(`绘制完成: ${type}`, 'success');
            console.log('绘制数据:', data);
        }

        // 测试函数
        window.testMapInit = function() {
            try {
                if (typeof BMap === 'undefined') {
                    log('百度地图API未加载', 'error');
                    return;
                }

                map = new MapAdapter('mapDiv', onDrawComplete, {
                    enablePerformanceMonitoring: true,
                    enableCaching: true,
                    enableErrorHandling: true
                });

                // 设置地图中心和缩放级别
                map.setCenter({ lng: 116.404, lat: 39.915 });
                map.setZoom(11);

                log('地图初始化成功', 'success');
                log(`适配器版本: ${AdapterUtils.getAdapterVersion(map)}`, 'info');
            } catch (error) {
                log(`地图初始化失败: ${error.message}`, 'error');
                console.error(error);
            }
        };

        window.testMapCenter = function() {
            if (!map) {
                log('请先初始化地图', 'error');
                return;
            }

            try {
                const newCenter = { lng: 121.473, lat: 31.230 }; // 上海
                map.setCenter(newCenter);
                log(`地图中心已设置为: ${newCenter.lng}, ${newCenter.lat}`, 'success');
            } catch (error) {
                log(`设置地图中心失败: ${error.message}`, 'error');
            }
        };

        window.testMapZoom = function() {
            if (!map) {
                log('请先初始化地图', 'error');
                return;
            }

            try {
                const newZoom = Math.floor(Math.random() * 10) + 5; // 5-14
                map.setZoom(newZoom);
                log(`缩放级别已设置为: ${newZoom}`, 'success');
            } catch (error) {
                log(`设置缩放级别失败: ${error.message}`, 'error');
            }
        };

        window.testMapDrag = function() {
            if (!map) {
                log('请先初始化地图', 'error');
                return;
            }

            try {
                map.targetDragging();
                const isDragEnabled = map.settings.dragEnabled;
                log(`拖拽状态: ${isDragEnabled ? '启用' : '禁用'}`, 'success');
            } catch (error) {
                log(`切换拖拽状态失败: ${error.message}`, 'error');
            }
        };

        window.testAddMarker = function() {
            if (!map) {
                log('请先初始化地图', 'error');
                return;
            }

            try {
                const point = { lng: 116.404 + Math.random() * 0.1, lat: 39.915 + Math.random() * 0.1 };
                const marker = map.addMarker(point, {
                    title: `标记点 ${overlays.length + 1}`
                });
                overlays.push(marker);
                log(`添加标记点成功: ${point.lng.toFixed(6)}, ${point.lat.toFixed(6)}`, 'success');
            } catch (error) {
                log(`添加标记点失败: ${error.message}`, 'error');
            }
        };

        window.testAddPolygon = function() {
            if (!map) {
                log('请先初始化地图', 'error');
                return;
            }

            try {
                const center = { lng: 116.404, lat: 39.915 };
                const points = [
                    { lng: center.lng - 0.01, lat: center.lat - 0.01 },
                    { lng: center.lng + 0.01, lat: center.lat - 0.01 },
                    { lng: center.lng + 0.01, lat: center.lat + 0.01 },
                    { lng: center.lng - 0.01, lat: center.lat + 0.01 }
                ];
                
                const polygon = map.addPolygon(points, {
                    fillColor: '#1890ff',
                    fillOpacity: 0.3,
                    strokeColor: '#096dd9',
                    strokeWeight: 2
                });
                overlays.push(polygon);
                log(`添加多边形成功，包含 ${points.length} 个点`, 'success');
            } catch (error) {
                log(`添加多边形失败: ${error.message}`, 'error');
            }
        };

        window.testAddPolyline = function() {
            if (!map) {
                log('请先初始化地图', 'error');
                return;
            }

            try {
                const center = { lng: 116.404, lat: 39.915 };
                const points = [
                    { lng: center.lng - 0.02, lat: center.lat },
                    { lng: center.lng, lat: center.lat + 0.02 },
                    { lng: center.lng + 0.02, lat: center.lat }
                ];
                
                const polyline = map.addPolyline(points, {
                    strokeColor: '#52c41a',
                    strokeWeight: 3,
                    strokeOpacity: 0.8
                });
                overlays.push(polyline);
                log(`添加折线成功，包含 ${points.length} 个点`, 'success');
            } catch (error) {
                log(`添加折线失败: ${error.message}`, 'error');
            }
        };

        window.testClearOverlays = function() {
            if (!map) {
                log('请先初始化地图', 'error');
                return;
            }

            try {
                map.clearOverlays();
                overlays = [];
                log('清空所有覆盖物成功', 'success');
            } catch (error) {
                log(`清空覆盖物失败: ${error.message}`, 'error');
            }
        };

        window.testOpenInfoWindow = function() {
            if (!map) {
                log('请先初始化地图', 'error');
                return;
            }

            try {
                const center = map.getCenter();
                map.openInfoWindow(
                    '<div style="padding: 10px;"><h3>测试信息窗口</h3><p>这是一个适配器测试信息窗口</p></div>',
                    center,
                    { title: '适配器测试' }
                );
                log('信息窗口已打开', 'success');
            } catch (error) {
                log(`打开信息窗口失败: ${error.message}`, 'error');
            }
        };

        window.testCloseInfoWindow = function() {
            if (!map) {
                log('请先初始化地图', 'error');
                return;
            }

            try {
                map.closeInfoWindow();
                log('信息窗口已关闭', 'success');
            } catch (error) {
                log(`关闭信息窗口失败: ${error.message}`, 'error');
            }
        };

        window.testMarkProvince = function() {
            if (!map) {
                log('请先初始化地图', 'error');
                return;
            }

            try {
                const provinces = ['北京市', '上海市', '广东省', '浙江省', '江苏省'];
                const randomProvince = provinces[Math.floor(Math.random() * provinces.length)];
                map.markProvince(randomProvince);
                log(`标记省份成功: ${randomProvince}`, 'success');
            } catch (error) {
                log(`标记省份失败: ${error.message}`, 'error');
            }
        };

        window.testClearProvince = function() {
            if (!map) {
                log('请先初始化地图', 'error');
                return;
            }

            try {
                map.clearMarkProvince();
                log('清除省份标记成功', 'success');
            } catch (error) {
                log(`清除省份标记失败: ${error.message}`, 'error');
            }
        };

        window.testPerformanceReport = function() {
            if (!map) {
                log('请先初始化地图', 'error');
                return;
            }

            try {
                const report = map.getPerformanceReport();
                log(`性能报告: ${JSON.stringify(report)}`, 'info');
            } catch (error) {
                log(`获取性能报告失败: ${error.message}`, 'error');
            }
        };

        window.testOptimizePerformance = function() {
            if (!map) {
                log('请先初始化地图', 'error');
                return;
            }

            try {
                map.optimizePerformance();
                log('性能优化完成', 'success');
            } catch (error) {
                log(`性能优化失败: ${error.message}`, 'error');
            }
        };

        window.testAdapterCompatibility = function() {
            if (!map) {
                log('请先初始化地图', 'error');
                return;
            }

            try {
                const compatibility = AdapterUtils.checkCompatibility(map);
                log(`兼容性检查: ${compatibility.isCompatible ? '通过' : '失败'}`, 
                    compatibility.isCompatible ? 'success' : 'error');
                
                if (compatibility.issues.length > 0) {
                    log(`问题: ${compatibility.issues.join(', ')}`, 'error');
                }
                
                if (compatibility.warnings.length > 0) {
                    log(`警告: ${compatibility.warnings.join(', ')}`, 'info');
                }
            } catch (error) {
                log(`兼容性检查失败: ${error.message}`, 'error');
            }
        };

        window.testAdapterVersion = function() {
            if (!map) {
                log('请先初始化地图', 'error');
                return;
            }

            try {
                const version = AdapterUtils.getAdapterVersion(map);
                const isAdapter = AdapterUtils.isAdapter(map);
                log(`适配器版本: ${version}`, 'info');
                log(`是否为适配器: ${isAdapter}`, 'info');
            } catch (error) {
                log(`获取版本信息失败: ${error.message}`, 'error');
            }
        };

        window.testLibInstance = function() {
            if (!map) {
                log('请先初始化地图', 'error');
                return;
            }

            try {
                const libInstance = AdapterUtils.getLibInstance(map);
                log(`lib库实例: ${libInstance ? '已获取' : '未找到'}`, 
                    libInstance ? 'success' : 'error');
                
                if (libInstance) {
                    log(`lib库类型: ${libInstance.constructor.name}`, 'info');
                }
            } catch (error) {
                log(`获取lib实例失败: ${error.message}`, 'error');
            }
        };

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            log('适配器测试页面已加载', 'info');
            log('请点击"初始化地图"开始测试', 'info');
            
            // 检查百度地图API
            if (typeof BMap !== 'undefined') {
                log('百度地图API已加载', 'success');
            } else {
                log('百度地图API未加载，请检查网络连接', 'error');
            }
        });
    </script>
</body>
</html>
