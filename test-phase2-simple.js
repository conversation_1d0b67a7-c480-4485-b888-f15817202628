/**
 * Phase 2 高级功能简化测试脚本
 * 验证图层系统、数据源系统、绘图工具适配器的基本功能
 */

// 模拟百度地图API和绘图管理器
global.BMap = {
  Map: class {
    constructor(container) {
      this.container = container;
      this.center = { lng: 116.404, lat: 39.915 };
      this.zoom = 11;
      this.overlays = [];
      this.listeners = new Map();
    }
    
    centerAndZoom(point, zoom) {
      this.center = point;
      this.zoom = zoom;
    }
    
    enableScrollWheelZoom() {}
    enableDragging() {}
    disableDragging() {}
    setCenter(point) { this.center = point; }
    getCenter() { return this.center; }
    setZoom(zoom) { this.zoom = zoom; }
    getZoom() { return this.zoom; }
    addOverlay(overlay) { this.overlays.push(overlay); }
    removeOverlay(overlay) {
      const index = this.overlays.indexOf(overlay);
      if (index > -1) this.overlays.splice(index, 1);
    }
    clearOverlays() { this.overlays = []; }
    addEventListener(event, handler) {
      if (!this.listeners.has(event)) this.listeners.set(event, []);
      this.listeners.get(event).push(handler);
    }
    removeEventListener(event, handler) {
      if (this.listeners.has(event)) {
        const handlers = this.listeners.get(event);
        const index = handlers.indexOf(handler);
        if (index > -1) handlers.splice(index, 1);
      }
    }
    openInfoWindow(infoWindow, point) {}
    closeInfoWindow() {}
  },
  
  Point: class {
    constructor(lng, lat) {
      this.lng = lng;
      this.lat = lat;
    }
  },
  
  Marker: class {
    constructor(point, options = {}) {
      this.point = point;
      this.options = options;
    }
    setPosition(point) { this.point = point; }
    getPosition() { return this.point; }
  },
  
  Polygon: class {
    constructor(points, options = {}) {
      this.points = points;
      this.options = options;
    }
    setPath(points) { this.points = points; }
    getPath() { return this.points; }
  },
  
  Polyline: class {
    constructor(points, options = {}) {
      this.points = points;
      this.options = options;
    }
    setPath(points) { this.points = points; }
    getPath() { return this.points; }
  },
  
  InfoWindow: class {
    constructor(content, options = {}) {
      this.content = content;
      this.options = options;
    }
    setContent(content) { this.content = content; }
    getContent() { return this.content; }
  }
};

// 模拟绘图管理器
global.BMAP_DRAWING_MARKER = 'marker';
global.BMAP_DRAWING_POLYGON = 'polygon';
global.BMAP_DRAWING_POLYLINE = 'polyline';
global.BMAP_DRAWING_CIRCLE = 'circle';
global.BMAP_DRAWING_RECTANGLE = 'rectangle';

global.BMapLib = {
  DrawingManager: class {
    constructor(map, options = {}) {
      this.map = map;
      this.options = options;
      this.isOpen = false;
      this.drawingMode = null;
      this.listeners = new Map();
    }
    
    open(mode) {
      this.isOpen = true;
      this.drawingMode = mode;
    }
    
    close() {
      this.isOpen = false;
      this.drawingMode = null;
    }
    
    addEventListener(event, handler) {
      if (!this.listeners.has(event)) this.listeners.set(event, []);
      this.listeners.get(event).push(handler);
    }
    
    removeEventListener(event, handler) {
      if (this.listeners.has(event)) {
        const handlers = this.listeners.get(event);
        const index = handlers.indexOf(handler);
        if (index > -1) handlers.splice(index, 1);
      }
    }
  }
};

// 测试函数
async function runPhase2Tests() {
  console.log('🚀 开始Phase 2高级功能测试...\n');
  
  let testResults = {
    total: 0,
    passed: 0,
    failed: 0,
    details: []
  };
  
  function logTest(name, success, message = '') {
    testResults.total++;
    if (success) {
      testResults.passed++;
      console.log(`✅ ${name}: 通过`);
    } else {
      testResults.failed++;
      console.log(`❌ ${name}: 失败 - ${message}`);
    }
    testResults.details.push({ name, success, message });
  }
  
  try {
    // 动态导入适配器
    const { 
      MapAdapter, 
      LayerSystemAdapter, 
      DataSourceAdapter, 
      DrawingToolAdapter,
      AdapterUtils 
    } = await import('./src/adapters/index.js');
    
    console.log('📦 适配器导入成功\n');
    
    // 测试1: 创建地图适配器
    console.log('🗺️ 测试组1: 地图适配器');
    let map = null;
    try {
      map = new MapAdapter('test-container', (type, data) => {
        console.log(`绘制完成: ${type}`);
      }, {
        enablePerformanceMonitoring: true,
        enableCaching: true
      });
      logTest('地图适配器创建', true);
    } catch (error) {
      logTest('地图适配器创建', false, error.message);
      return testResults;
    }
    
    // 测试2: 图层系统适配器
    console.log('\n📋 测试组2: 图层系统适配器');
    let layerSystem = null;
    try {
      layerSystem = new LayerSystemAdapter(map);
      await layerSystem.init({});
      logTest('图层系统初始化', layerSystem.isReady());
    } catch (error) {
      logTest('图层系统初始化', false, error.message);
    }
    
    if (layerSystem && layerSystem.isReady()) {
      try {
        const layerConfig = {
          id: 'test-layer-1',
          name: '测试图层',
          type: 'marker',
          visible: true,
          data: {
            type: 'FeatureCollection',
            features: [{
              type: 'Feature',
              geometry: {
                type: 'Point',
                coordinates: [116.404, 39.915]
              },
              properties: { name: '测试点' }
            }]
          }
        };
        
        await layerSystem.addLayer(layerConfig);
        logTest('添加图层', true);
        
        layerSystem.setLayerVisibility('test-layer-1', false);
        logTest('设置图层可见性', true);
        
        layerSystem.setLayerOpacity('test-layer-1', 0.5);
        logTest('设置图层透明度', true);
        
        const stats = layerSystem.getLayerStatistics('test-layer-1');
        logTest('获取图层统计', stats !== null);
        
      } catch (error) {
        logTest('图层操作', false, error.message);
      }
    }
    
    // 测试3: 数据源系统适配器
    console.log('\n💾 测试组3: 数据源系统适配器');
    let dataSourceSystem = null;
    try {
      dataSourceSystem = new DataSourceAdapter();
      await dataSourceSystem.init({});
      logTest('数据源系统初始化', dataSourceSystem.isReady());
    } catch (error) {
      logTest('数据源系统初始化', false, error.message);
    }
    
    if (dataSourceSystem && dataSourceSystem.isReady()) {
      try {
        const dataSourceConfig = {
          id: 'test-datasource-1',
          name: '测试数据源',
          type: 'static',
          staticData: {
            type: 'FeatureCollection',
            features: [{
              type: 'Feature',
              geometry: {
                type: 'Point',
                coordinates: [116.404, 39.915]
              },
              properties: { name: '数据源测试点' }
            }]
          }
        };
        
        await dataSourceSystem.addDataSource(dataSourceConfig);
        logTest('添加数据源', true);
        
        const data = await dataSourceSystem.loadData('test-datasource-1');
        logTest('加载数据', data !== null);
        
        const stats = dataSourceSystem.getDataSourceStatistics('test-datasource-1');
        logTest('获取数据源统计', stats !== null);
        
      } catch (error) {
        logTest('数据源操作', false, error.message);
      }
    }
    
    // 测试4: 绘图工具适配器
    console.log('\n✏️ 测试组4: 绘图工具适配器');
    let drawingTool = null;
    try {
      drawingTool = new DrawingToolAdapter(map._map, {
        onDrawComplete: (type, data) => {
          console.log(`绘制完成: ${type}`);
        }
      });
      logTest('绘图工具创建', true);
    } catch (error) {
      logTest('绘图工具创建', false, error.message);
    }
    
    if (drawingTool) {
      try {
        drawingTool.open(global.BMAP_DRAWING_MARKER);
        logTest('开启绘制模式', drawingTool.isDrawing());
        
        drawingTool.close();
        logTest('关闭绘制模式', !drawingTool.isDrawing());
        
        drawingTool.updateStyle({
          strokeColor: '#ff0000',
          fillColor: '#00ff00'
        });
        logTest('更新绘制样式', true);
        
        const canUndo = drawingTool.canUndo();
        const canRedo = drawingTool.canRedo();
        logTest('撤销重做状态检查', typeof canUndo === 'boolean' && typeof canRedo === 'boolean');
        
      } catch (error) {
        logTest('绘图工具操作', false, error.message);
      }
    }
    
    // 测试5: 适配器工具函数
    console.log('\n🔧 测试组5: 适配器工具函数');
    try {
      const isMapAdapter = AdapterUtils.isAdapter(map);
      logTest('适配器识别', isMapAdapter);
      
      const version = AdapterUtils.getAdapterVersion(map);
      logTest('获取适配器版本', version !== 'unknown');
      
      const libInstance = AdapterUtils.getLibInstance(map);
      logTest('获取lib库实例', libInstance !== null);
      
      const compatibility = AdapterUtils.checkCompatibility(map);
      logTest('兼容性检查', compatibility.isCompatible);
      
    } catch (error) {
      logTest('适配器工具函数', false, error.message);
    }
    
    // 测试6: 集成测试
    console.log('\n🔗 测试组6: 集成测试');
    try {
      // 测试地图与图层系统的集成
      if (map && layerSystem) {
        const mapCenter = map.getCenter();
        const layerCount = layerSystem.getLayerCount();
        logTest('地图-图层系统集成', mapCenter && layerCount >= 0);
      }
      
      // 测试地图与绘图工具的集成
      if (map && drawingTool) {
        const mapInstance = map.getMap();
        const drawingManager = drawingTool.getDrawingManager();
        logTest('地图-绘图工具集成', mapInstance && drawingManager);
      }
      
      // 测试数据源与图层的集成
      if (dataSourceSystem && layerSystem) {
        const dataSourceCount = dataSourceSystem.getDataSourceCount();
        const layerCount = layerSystem.getLayerCount();
        logTest('数据源-图层集成', dataSourceCount >= 0 && layerCount >= 0);
      }
      
    } catch (error) {
      logTest('集成测试', false, error.message);
    }
    
    // 测试7: 清理测试
    console.log('\n🧹 测试组7: 清理测试');
    try {
      if (drawingTool) {
        drawingTool.destroy();
        logTest('绘图工具销毁', true);
      }
      
      if (dataSourceSystem) {
        dataSourceSystem.destroy();
        logTest('数据源系统销毁', true);
      }
      
      if (layerSystem) {
        layerSystem.destroy();
        logTest('图层系统销毁', true);
      }
      
      if (map) {
        map.destroy();
        logTest('地图适配器销毁', true);
      }
      
    } catch (error) {
      logTest('清理测试', false, error.message);
    }
    
  } catch (error) {
    console.error('❌ 测试执行失败:', error.message);
    console.error(error.stack);
  }
  
  // 输出测试结果
  console.log('\n📊 测试结果汇总:');
  console.log(`总测试数: ${testResults.total}`);
  console.log(`通过: ${testResults.passed}`);
  console.log(`失败: ${testResults.failed}`);
  console.log(`成功率: ${((testResults.passed / testResults.total) * 100).toFixed(1)}%`);
  
  if (testResults.failed > 0) {
    console.log('\n❌ 失败的测试:');
    testResults.details
      .filter(test => !test.success)
      .forEach(test => {
        console.log(`  - ${test.name}: ${test.message}`);
      });
  }
  
  console.log('\n🎉 Phase 2 高级功能测试完成！');
  
  return testResults;
}

// 如果直接运行此脚本
if (import.meta.url === `file://${process.argv[1]}`) {
  runPhase2Tests();
}

export { runPhase2Tests };
