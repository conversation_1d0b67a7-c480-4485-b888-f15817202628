/**
 * 简单的适配器测试脚本
 * 用于验证适配器的基本功能
 */

// 模拟百度地图API
global.BMap = {
  Map: class {
    constructor(container) {
      this.container = container;
      this.center = { lng: 116.404, lat: 39.915 };
      this.zoom = 11;
      this.overlays = [];
      this.listeners = new Map();
    }
    
    centerAndZoom(point, zoom) {
      this.center = point;
      this.zoom = zoom;
    }
    
    enableScrollWheelZoom() {}
    enableDragging() {}
    disableDragging() {}
    
    setCenter(point) {
      this.center = point;
    }
    
    getCenter() {
      return this.center;
    }
    
    setZoom(zoom) {
      this.zoom = zoom;
    }
    
    getZoom() {
      return this.zoom;
    }
    
    addOverlay(overlay) {
      this.overlays.push(overlay);
    }
    
    removeOverlay(overlay) {
      const index = this.overlays.indexOf(overlay);
      if (index > -1) {
        this.overlays.splice(index, 1);
      }
    }
    
    clearOverlays() {
      this.overlays = [];
    }
    
    addEventListener(event, handler) {
      if (!this.listeners.has(event)) {
        this.listeners.set(event, []);
      }
      this.listeners.get(event).push(handler);
    }
    
    removeEventListener(event, handler) {
      if (this.listeners.has(event)) {
        const handlers = this.listeners.get(event);
        const index = handlers.indexOf(handler);
        if (index > -1) {
          handlers.splice(index, 1);
        }
      }
    }
    
    openInfoWindow(infoWindow, point) {
      console.log('Opening info window at:', point);
    }
    
    closeInfoWindow() {
      console.log('Closing info window');
    }
  },
  
  Point: class {
    constructor(lng, lat) {
      this.lng = lng;
      this.lat = lat;
    }
  },
  
  Marker: class {
    constructor(point, options = {}) {
      this.point = point;
      this.options = options;
    }
    
    setPosition(point) {
      this.point = point;
    }
    
    getPosition() {
      return this.point;
    }
  },
  
  Polygon: class {
    constructor(points, options = {}) {
      this.points = points;
      this.options = options;
    }
    
    setPath(points) {
      this.points = points;
    }
    
    getPath() {
      return this.points;
    }
  },
  
  Polyline: class {
    constructor(points, options = {}) {
      this.points = points;
      this.options = options;
    }
    
    setPath(points) {
      this.points = points;
    }
    
    getPath() {
      return this.points;
    }
  },
  
  InfoWindow: class {
    constructor(content, options = {}) {
      this.content = content;
      this.options = options;
    }
    
    setContent(content) {
      this.content = content;
    }
    
    getContent() {
      return this.content;
    }
  }
};

// 模拟绘图管理器
global.BMAP_DRAWING_MARKER = 'marker';
global.BMAP_DRAWING_POLYGON = 'polygon';
global.BMAP_DRAWING_POLYLINE = 'polyline';
global.BMAP_DRAWING_CIRCLE = 'circle';
global.BMAP_DRAWING_RECTANGLE = 'rectangle';

global.BMapLib = {
  DrawingManager: class {
    constructor(map, options = {}) {
      this.map = map;
      this.options = options;
      this.isOpen = false;
      this.drawingMode = null;
    }
    
    open(mode) {
      this.isOpen = true;
      this.drawingMode = mode;
    }
    
    close() {
      this.isOpen = false;
      this.drawingMode = null;
    }
    
    addEventListener(event, handler) {
      // 模拟事件监听
    }
    
    removeEventListener(event, handler) {
      // 模拟事件移除
    }
  }
};

// 测试函数
async function runTests() {
  console.log('🧪 开始适配器测试...\n');
  
  try {
    // 动态导入适配器（需要在Node.js环境中运行）
    const { MapAdapter, AdapterUtils } = await import('./src/adapters/index.js');
    
    console.log('✅ 适配器导入成功');
    
    // 测试1: 创建地图适配器
    console.log('\n📍 测试1: 创建地图适配器');
    const map = new MapAdapter('test-container', (type, data) => {
      console.log(`绘制完成: ${type}`, data);
    }, {
      enablePerformanceMonitoring: true,
      enableCaching: true
    });
    
    console.log('✅ 地图适配器创建成功');
    console.log(`   - 适配器版本: ${AdapterUtils.getAdapterVersion(map)}`);
    console.log(`   - 是否为适配器: ${AdapterUtils.isAdapter(map)}`);
    
    // 测试2: 基础地图操作
    console.log('\n🗺️ 测试2: 基础地图操作');
    
    // 设置中心点
    const newCenter = { lng: 121.473, lat: 31.230 };
    map.setCenter(newCenter);
    const center = map.getCenter();
    console.log(`✅ 设置中心点: ${center.lng}, ${center.lat}`);
    
    // 设置缩放级别
    map.setZoom(15);
    const zoom = map.getZoom();
    console.log(`✅ 设置缩放级别: ${zoom}`);
    
    // 测试拖拽
    map.targetDragging(false);
    console.log(`✅ 拖拽状态: ${map.settings.dragEnabled ? '启用' : '禁用'}`);
    
    // 测试3: 覆盖物操作
    console.log('\n📍 测试3: 覆盖物操作');
    
    // 添加标记点
    const marker = map.addMarker({ lng: 121.473, lat: 31.230 }, {
      title: '测试标记点'
    });
    console.log('✅ 添加标记点成功');
    
    // 添加多边形
    const polygonPoints = [
      { lng: 121.470, lat: 31.228 },
      { lng: 121.476, lat: 31.228 },
      { lng: 121.476, lat: 31.232 },
      { lng: 121.470, lat: 31.232 }
    ];
    const polygon = map.addPolygon(polygonPoints, {
      fillColor: '#1890ff',
      fillOpacity: 0.3
    });
    console.log('✅ 添加多边形成功');
    
    // 添加折线
    const polylinePoints = [
      { lng: 121.468, lat: 31.230 },
      { lng: 121.473, lat: 31.235 },
      { lng: 121.478, lat: 31.230 }
    ];
    const polyline = map.addPolyline(polylinePoints, {
      strokeColor: '#52c41a',
      strokeWeight: 3
    });
    console.log('✅ 添加折线成功');
    
    // 测试4: 信息窗口
    console.log('\n💬 测试4: 信息窗口');
    
    map.openInfoWindow(
      '<div>测试信息窗口</div>',
      center,
      { title: '测试' }
    );
    console.log('✅ 打开信息窗口成功');
    
    map.closeInfoWindow();
    console.log('✅ 关闭信息窗口成功');
    
    // 测试5: 省份多边形
    console.log('\n🏛️ 测试5: 省份多边形');
    
    map.markProvince('上海市');
    console.log('✅ 标记省份成功');
    
    map.clearMarkProvince();
    console.log('✅ 清除省份标记成功');
    
    // 测试6: 性能功能
    console.log('\n⚡ 测试6: 性能功能');
    
    try {
      const report = map.getPerformanceReport();
      console.log('✅ 获取性能报告成功');
    } catch (error) {
      console.log('⚠️ 性能报告功能需要完整环境');
    }
    
    try {
      map.optimizePerformance();
      console.log('✅ 性能优化执行成功');
    } catch (error) {
      console.log('⚠️ 性能优化功能需要完整环境');
    }
    
    // 测试7: 适配器特性
    console.log('\n🔧 测试7: 适配器特性');
    
    const compatibility = AdapterUtils.checkCompatibility(map);
    console.log(`✅ 兼容性检查: ${compatibility.isCompatible ? '通过' : '失败'}`);
    
    if (compatibility.issues.length > 0) {
      console.log(`⚠️ 发现问题: ${compatibility.issues.join(', ')}`);
    }
    
    if (compatibility.warnings.length > 0) {
      console.log(`⚠️ 警告: ${compatibility.warnings.join(', ')}`);
    }
    
    const libInstance = AdapterUtils.getLibInstance(map);
    console.log(`✅ lib库实例: ${libInstance ? '已获取' : '未找到'}`);
    
    // 测试8: 清理
    console.log('\n🧹 测试8: 清理');
    
    map.clearOverlays();
    console.log('✅ 清空覆盖物成功');
    
    map.destroy();
    console.log('✅ 销毁地图成功');
    
    console.log('\n🎉 所有测试完成！适配器工作正常。');
    
  } catch (error) {
    console.error('❌ 测试失败:', error.message);
    console.error(error.stack);
  }
}

// 如果直接运行此脚本
if (import.meta.url === `file://${process.argv[1]}`) {
  runTests();
}

export { runTests };
