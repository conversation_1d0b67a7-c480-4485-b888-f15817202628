<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>地图组件库完整示例</title>
    <script type="text/javascript" src="https://api.map.baidu.com/api?v=3.0&ak=YOUR_API_KEY"></script>
    <script type="text/javascript" src="https://api.map.baidu.com/library/DrawingManager/1.4/src/DrawingManager_min.js"></script>
    <link rel="stylesheet" href="https://api.map.baidu.com/library/DrawingManager/1.4/src/DrawingManager_min.css" />
    <link rel="stylesheet" href="./components/components.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON><PERSON>, sans-serif;
            background: #f5f5f5;
        }
        
        .app-container {
            display: flex;
            height: 100vh;
        }
        
        .sidebar {
            width: 320px;
            background: white;
            border-right: 1px solid #e0e0e0;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }
        
        .sidebar-header {
            padding: 20px;
            border-bottom: 1px solid #e0e0e0;
            background: #fafafa;
        }
        
        .sidebar-header h1 {
            font-size: 18px;
            color: #333;
            margin-bottom: 8px;
        }
        
        .sidebar-header p {
            font-size: 14px;
            color: #666;
        }
        
        .sidebar-content {
            flex: 1;
            overflow-y: auto;
            padding: 20px;
        }
        
        .control-section {
            margin-bottom: 24px;
        }
        
        .control-section h3 {
            font-size: 14px;
            font-weight: 600;
            color: #333;
            margin-bottom: 12px;
            padding-bottom: 6px;
            border-bottom: 1px solid #e0e0e0;
        }
        
        .control-item {
            margin-bottom: 16px;
        }
        
        .control-item label {
            display: block;
            font-size: 12px;
            color: #666;
            margin-bottom: 6px;
        }
        
        .map-container {
            flex: 1;
            position: relative;
        }
        
        #mapDiv {
            width: 100%;
            height: 100%;
        }
        
        .status-bar {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 40px;
            background: rgba(255, 255, 255, 0.95);
            border-top: 1px solid #e0e0e0;
            display: flex;
            align-items: center;
            padding: 0 16px;
            font-size: 12px;
            color: #666;
        }
        
        .theme-toggle {
            position: absolute;
            top: 20px;
            right: 20px;
            z-index: 1000;
        }
        
        .theme-toggle button {
            padding: 8px 16px;
            background: white;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .theme-toggle button:hover {
            background: #f5f5f5;
        }
        
        /* 响应式设计 */
        @media (max-width: 768px) {
            .app-container {
                flex-direction: column;
            }
            
            .sidebar {
                width: 100%;
                height: 300px;
            }
            
            .map-container {
                height: calc(100vh - 300px);
            }
        }
    </style>
</head>
<body>
    <div class="app-container">
        <!-- 侧边栏 -->
        <div class="sidebar">
            <div class="sidebar-header">
                <h1>🗺️ 地图组件库</h1>
                <p>完整的企业级地图解决方案</p>
            </div>
            
            <div class="sidebar-content">
                <!-- 工具栏控制 -->
                <div class="control-section">
                    <h3>🔧 绘制工具</h3>
                    <div id="toolbarContainer"></div>
                </div>
                
                <!-- 样式控制 -->
                <div class="control-section">
                    <h3>🎨 样式设置</h3>
                    <div class="control-item">
                        <label>填充颜色</label>
                        <div id="fillColorPicker"></div>
                    </div>
                    <div class="control-item">
                        <label>边框颜色</label>
                        <div id="strokeColorPicker"></div>
                    </div>
                    <div class="control-item">
                        <label>标记图标</label>
                        <div id="iconSelector"></div>
                    </div>
                </div>
                
                <!-- 图层控制 -->
                <div class="control-section">
                    <h3>📋 图层管理</h3>
                    <div id="layerPanel"></div>
                </div>
                
                <!-- 时间控制 -->
                <div class="control-section">
                    <h3>⏰ 时间控制</h3>
                    <div id="timeSlider"></div>
                </div>
            </div>
        </div>
        
        <!-- 地图区域 -->
        <div class="map-container">
            <div id="mapDiv"></div>
            
            <!-- 主题切换 -->
            <div class="theme-toggle">
                <button onclick="toggleTheme()">🌓 切换主题</button>
            </div>
            
            <!-- 状态栏 -->
            <div class="status-bar">
                <span id="statusText">地图已加载，选择工具开始绘制</span>
            </div>
        </div>
    </div>

    <script type="module">
        import Map from './model/Map.js';
        import { componentManager } from './components/index.js';
        import eventBus from './utils/event-bus.js';

        let map = null;
        let currentTheme = 'light';
        let components = {};

        // 初始化应用
        async function initApp() {
            try {
                // 等待百度地图API加载
                if (typeof BMap === 'undefined') {
                    throw new Error('百度地图API未加载');
                }

                // 创建地图实例
                map = new Map('mapDiv', onDrawComplete, {
                    enablePerformanceMonitoring: true,
                    enableCaching: true,
                    enableErrorHandling: true
                });

                // 设置地图中心和缩放级别
                map.centerAndZoom(new BMap.Point(116.404, 39.915), 11);

                // 初始化UI组件
                initComponents();

                // 绑定事件
                bindEvents();

                updateStatus('应用初始化完成');
            } catch (error) {
                console.error('应用初始化失败:', error);
                updateStatus(`初始化失败: ${error.message}`);
            }
        }

        // 初始化UI组件
        function initComponents() {
            // 工具栏
            components.toolbar = componentManager.create('toolbar', '#toolbarContainer', {
                orientation: 'vertical',
                size: 'small',
                onToolClick: (toolId, tool, activeTool) => {
                    handleToolClick(toolId, activeTool);
                }
            });
            componentManager.register('toolbar', components.toolbar);

            // 颜色选择器
            components.fillColorPicker = componentManager.create('colorPicker', '#fillColorPicker', {
                defaultColor: '#1890ff',
                size: 'small',
                onChange: (color) => {
                    updateDrawingStyle({ fillColor: color });
                }
            });
            componentManager.register('fillColorPicker', components.fillColorPicker);

            components.strokeColorPicker = componentManager.create('colorPicker', '#strokeColorPicker', {
                defaultColor: '#096dd9',
                size: 'small',
                onChange: (color) => {
                    updateDrawingStyle({ strokeColor: color });
                }
            });
            componentManager.register('strokeColorPicker', components.strokeColorPicker);

            // 图标选择器
            components.iconSelector = componentManager.create('iconSelector', '#iconSelector', {
                size: 'small',
                onChange: (icon) => {
                    updateStatus(`选择了图标: ${icon ? icon.id : '无'}`);
                }
            });
            componentManager.register('iconSelector', components.iconSelector);

            // 图层面板
            components.layerPanel = componentManager.create('layerPanel', '#layerPanel', {
                layerSystem: map.layerSystem,
                onLayerChange: (action, layerId, value) => {
                    updateStatus(`图层操作: ${action} - ${layerId}`);
                }
            });
            componentManager.register('layerPanel', components.layerPanel);

            // 时间滑块
            components.timeSlider = componentManager.create('timeSlider', '#timeSlider', {
                startTime: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000), // 7天前
                endTime: new Date(), // 现在
                onChange: (time) => {
                    updateStatus(`时间: ${time.toLocaleString()}`);
                },
                onPlay: () => {
                    updateStatus('时间动画播放中...');
                },
                onPause: () => {
                    updateStatus('时间动画已暂停');
                }
            });
            componentManager.register('timeSlider', components.timeSlider);
        }

        // 绑定事件
        function bindEvents() {
            // 监听地图事件
            eventBus.on('map:click', (data) => {
                updateStatus(`地图点击: ${data.point.lng.toFixed(6)}, ${data.point.lat.toFixed(6)}`);
            });

            // 监听绘制事件
            eventBus.on('drawingComplete', (data) => {
                updateStatus(`绘制完成: ${data.drawingMode}`);
            });

            // 监听性能事件
            eventBus.on('performance:warning', (data) => {
                updateStatus(`性能警告: ${data.message}`);
            });
        }

        // 处理工具点击
        function handleToolClick(toolId, activeTool) {
            updateStatus(`工具: ${toolId} ${activeTool ? '已激活' : '已取消'}`);
            
            // 根据工具类型执行相应操作
            switch (toolId) {
                case 'marker':
                case 'polygon':
                case 'polyline':
                case 'circle':
                case 'rectangle':
                    if (activeTool) {
                        map.drawingTool.open(getDrawingMode(toolId));
                    } else {
                        map.drawingTool.close();
                    }
                    break;
                case 'clear':
                    map.drawingTool.emptyOverlay();
                    updateStatus('已清空所有绘制内容');
                    break;
            }
        }

        // 获取绘制模式
        function getDrawingMode(toolId) {
            const modes = {
                marker: BMAP_DRAWING_MARKER,
                polygon: BMAP_DRAWING_POLYGON,
                polyline: BMAP_DRAWING_POLYLINE,
                circle: BMAP_DRAWING_CIRCLE,
                rectangle: BMAP_DRAWING_RECTANGLE
            };
            return modes[toolId];
        }

        // 更新绘制样式
        function updateDrawingStyle(style) {
            if (map && map.drawingTool) {
                map.drawingTool.updateStyle(style);
                updateStatus(`样式已更新: ${JSON.stringify(style)}`);
            }
        }

        // 绘制完成回调
        function onDrawComplete(type, data) {
            updateStatus(`绘制完成: ${type}`);
            console.log('绘制数据:', data);
        }

        // 更新状态
        function updateStatus(message) {
            const statusElement = document.getElementById('statusText');
            const timestamp = new Date().toLocaleTimeString();
            statusElement.textContent = `[${timestamp}] ${message}`;
        }

        // 切换主题
        window.toggleTheme = function() {
            currentTheme = currentTheme === 'light' ? 'dark' : 'light';
            componentManager.setTheme(currentTheme);
            document.documentElement.setAttribute('data-theme', currentTheme);
            updateStatus(`切换到${currentTheme === 'light' ? '浅色' : '深色'}主题`);
        };

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 检查百度地图API
            if (typeof BMap !== 'undefined') {
                initApp();
            } else {
                // 等待API加载
                const checkAPI = setInterval(() => {
                    if (typeof BMap !== 'undefined') {
                        clearInterval(checkAPI);
                        initApp();
                    }
                }, 100);
                
                // 10秒后超时
                setTimeout(() => {
                    clearInterval(checkAPI);
                    updateStatus('百度地图API加载超时，请检查网络连接');
                }, 10000);
            }
        });

        // 页面卸载时清理资源
        window.addEventListener('beforeunload', () => {
            if (map) {
                map.destroy();
            }
            componentManager.destroyAll();
        });
    </script>
</body>
</html>
