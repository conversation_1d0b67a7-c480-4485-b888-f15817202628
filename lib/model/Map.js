import DrawingTool from "./DrawingTool.js";
import MapSettings from "./MapSettings.js";
import ProvincePolygon from "./ProvincePolygon.js";
import InfoWindow from "./InfoWindow.js";
import LayerSystem from "./layer/index.js";
import eventBus, { eventMap } from "../utils/event-bus";

export default class Map {
  constructor(dom, drawCallback, settings) {
    this._map = new BMap.Map(dom);
    this.settings = new MapSettings(settings);
    this.drawCallback = drawCallback;

    // 临时状态变量
    this.isOpenCenterModal = false;
    this.openCenterModalCb = null;
    this.tempClickPoint = null;
    this.tempCenterPoint = null;

    this._initMap();
    this.initDrawingTool();
    this.initProvincePolygon();
    this.initInfoWindow();
    this.initLayerSystem();
    this.initEvent();

    // 通知地图初始化完成
    eventBus.emit(eventMap.mapInit, this);
  }

  _initMap() {
    // 初始化地图，设置中心点坐标和地图级别
    this._map.centerAndZoom(
      new BMap.Point(this.settings.center.lng, this.settings.center.lat),
      this.settings.zoom,
    );
    this._map.enableScrollWheelZoom(true); // 开启鼠标滚轮缩放
  }

  // 初始化事件
  initEvent() {
    // 监听设置变更
    eventBus.on(eventMap.mapSettingsChange, (settings) => {
      this.updateMapSettings(settings);
    });

    // 监听地图点击事件，用于取消省份多边形高亮
    this._map.addEventListener("click", (ev) => {
      this.provincePolygon?.cancelHightLight(ev);
    });
  }

  // 更新地图设置
  updateMapSettings(settings) {
    if (settings.zoom !== this._map.getZoom()) {
      this._map.setZoom(settings.zoom);
    }

    if (settings.dragEnabled !== this._map.draggingEnabled) {
      settings.dragEnabled ? this._map.enableDragging() : this._map.disableDragging();
    }

    // 更新中心点
    if (
      settings.center &&
      (settings.center.lng !== this._map.getCenter().lng ||
        settings.center.lat !== this._map.getCenter().lat)
    ) {
      const point = new BMap.Point(settings.center.lng, settings.center.lat);
      this._map.setCenter(point);
      this._map.panTo(point);
    }
  }

  // 初始化绘图工具
  initDrawingTool() {
    this.drawingTool = new DrawingTool(this._map, {
      drawCallback: this.drawCallback,
    });
  }

  // 初始化省份多边形
  initProvincePolygon() {
    this.provincePolygon = new ProvincePolygon(this._map, this.settings?.provincePolygonConfig);
  }

  // 初始化信息窗口
  initInfoWindow() {
    this._infoWindow = new InfoWindow();
    this.infoWindow = this._infoWindow.getInfoWindow();
  }

  // 初始化图层系统
  initLayerSystem() {
    this.layerSystem = new LayerSystem(this._map);
  }

  // 初始化设置
  initSettings(settings) {
    this.settings = new MapSettings(settings);
    this.setZoom(this.settings.zoom);
    this.targetDragging(this.settings.dragEnabled);
    this.setCenter(this.settings.center);
  }

  // 管理拖拽
  targetDragging(flag) {
    if (flag === undefined) {
      this.settings.change("dragEnabled", !this.settings.dragEnabled);
    } else {
      this.settings.change("dragEnabled", flag);
    }
  }

  // 确认修改地图中心点
  confirmChangeCenter(isCancel = false) {
    if (!isCancel && this.tempClickPoint) {
      this.setCenter(this.tempClickPoint);
    }

    this._cleanupCenterSelection();
  }

  // 清理中心点选择状态
  _cleanupCenterSelection() {
    if (this.openCenterModalCb) {
      this._map.removeEventListener("click", this.openCenterModalCb);
    }

    if (this.tempCenterPoint) {
      this._map.removeOverlay(this.tempCenterPoint);
    }

    this.isOpenCenterModal = false;
    this.openCenterModalCb = null;
    this.tempClickPoint = null;
    this.tempCenterPoint = null;
  }

  // 标记地图中心点
  markCenterPoint(center) {
    if (this.tempCenterPoint) {
      this.tempCenterPoint.setPosition(center);
      return;
    }

    this.tempCenterPoint = new BMap.Marker(center, {
      id: "default-center-point",
      name: "地图中心点",
    });
    this._map.addOverlay(this.tempCenterPoint);
  }

  // 点击选择地图中心
  _clickSelectCenter({ point }) {
    this.tempClickPoint = point;
    this.markCenterPoint(point);
  }

  // 打开中心点选择模式
  openCenterModal() {
    if (this.isOpenCenterModal) return;

    this._cleanupCenterSelection();
    this.isOpenCenterModal = true;
    this.openCenterModalCb = this._clickSelectCenter.bind(this);
    this._map.addEventListener("click", this.openCenterModalCb);
    this.markCenterPoint(this.settings.center);
  }

  // 设置地图中心
  setCenter(point) {
    if (!(point instanceof BMap.Point)) {
      point = new BMap.Point(point.lng, point.lat);
    }
    this._map.setCenter(point);
    this._map.panTo(point);
    this.settings.change("center", point);
  }

  getCenter() {
    return this._map.getCenter();
  }

  // 设置缩放级别
  setZoom(zoom) {
    this._map.setZoom(zoom);
    this.settings.change("zoom", zoom);
  }

  getZoom() {
    return this._map.getZoom();
  }

  /**
   * 打开信息窗口
   * @param {BMap.Point} position 位置
   * @param {Object} options 选项
   */
  openInfoWindow(position, options = {}) {
    if (!position) throw new Error("没有传入位置实例");

    if (options.title) {
      this._infoWindow.updateTitle(options.title);
    }

    if (options.content) {
      this._infoWindow.updateContent(options.content);
    }

    // 添加事件监听器
    if (options.onConfirm) {
      this._infoWindow.addEventListener("confirm", options.onConfirm);
    }

    if (options.onCancel) {
      this._infoWindow.addEventListener("cancel", options.onCancel);
    }

    this._map.openInfoWindow(this.infoWindow, position);
  }

  /**
   * 关闭信息窗口
   */
  closeInfoWindow() {
    this._map.closeInfoWindow();
  }

  // ==================== 图层系统方法 ====================

  /**
   * 创建图层
   * @param {Object} config 图层配置
   * @returns {String} 图层ID
   */
  createLayer(config = {}) {
    return this.layerSystem.createLayer(config);
  }

  /**
   * 删除图层
   * @param {String} layerId 图层ID
   * @returns {Boolean} 是否删除成功
   */
  deleteLayer(layerId) {
    return this.layerSystem.deleteLayer(layerId);
  }

  /**
   * 获取图层
   * @param {String} layerId 图层ID
   * @returns {Object|null} 图层对象
   */
  getLayer(layerId) {
    return this.layerSystem.getLayer(layerId);
  }

  /**
   * 获取所有图层
   * @returns {Array} 图层数组
   */
  getAllLayers() {
    return this.layerSystem.getAllLayers();
  }

  /**
   * 显示图层
   * @param {String} layerId 图层ID
   * @returns {Boolean} 是否成功
   */
  showLayer(layerId) {
    return this.layerSystem.showLayer(layerId);
  }

  /**
   * 隐藏图层
   * @param {String} layerId 图层ID
   * @returns {Boolean} 是否成功
   */
  hideLayer(layerId) {
    return this.layerSystem.hideLayer(layerId);
  }

  /**
   * 切换图层显示状态
   * @param {String} layerId 图层ID
   * @returns {Boolean} 切换后的显示状态
   */
  toggleLayer(layerId) {
    return this.layerSystem.toggleLayer(layerId);
  }

  /**
   * 选择图层
   * @param {String} layerId 图层ID
   * @returns {Boolean} 是否成功
   */
  selectLayer(layerId) {
    return this.layerSystem.selectLayer(layerId);
  }

  /**
   * 获取当前选中的图层
   * @returns {Object|null} 选中的图层
   */
  getSelectedLayer() {
    return this.layerSystem.getSelectedLayer();
  }

  // 导出配置
  exportConfig() {
    return this.settings;
  }

  // 销毁地图
  destroy() {
    this._cleanupCenterSelection();

    // 清理信息窗口
    if (this._infoWindow) {
      this._infoWindow.destroy();
      this._infoWindow = null;
      this.infoWindow = null;
    }

    // 清理省份多边形
    if (this.provincePolygon) {
      this.provincePolygon.clearMarkProvince();
      this.provincePolygon = null;
    }

    eventBus.emit(eventMap.mapDestroy, this);
    this._map = null;
  }
}
