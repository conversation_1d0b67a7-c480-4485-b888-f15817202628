import DrawingTool from "./DrawingTool.js";
import MapSettings from "./MapSettings.js";
import eventBus, { eventMap } from "../utils/event-bus";

export default class Map {
  constructor(dom, drawCallback, settings) {
    this._map = new BMap.Map(dom);
    this.settings = new MapSettings(settings);
    this.drawCallback = drawCallback;

    // 临时状态变量
    this.isOpenCenterModal = false;
    this.openCenterModalCb = null;
    this.tempClickPoint = null;
    this.tempCenterPoint = null;

    this._initMap();
    this.initDrawingTool();
    this.initEvent();

    // 通知地图初始化完成
    eventBus.emit(eventMap.mapInit, this);
  }

  _initMap() {
    // 初始化地图，设置中心点坐标和地图级别
    this._map.centerAndZoom(
      new BMap.Point(this.settings.center.lng, this.settings.center.lat),
      this.settings.zoom,
    );
    this._map.enableScrollWheelZoom(true); // 开启鼠标滚轮缩放
  }

  // 初始化事件
  initEvent() {
    // 监听设置变更
    eventBus.on(eventMap.mapSettingsChange, (settings) => {
      this.updateMapSettings(settings);
    });
  }

  // 更新地图设置
  updateMapSettings(settings) {
    if (settings.zoom !== this._map.getZoom()) {
      this._map.setZoom(settings.zoom);
    }

    if (settings.dragEnabled !== this._map.draggingEnabled) {
      settings.dragEnabled ? this._map.enableDragging() : this._map.disableDragging();
    }

    // 更新中心点
    if (
      settings.center &&
      (settings.center.lng !== this._map.getCenter().lng ||
        settings.center.lat !== this._map.getCenter().lat)
    ) {
      const point = new BMap.Point(settings.center.lng, settings.center.lat);
      this._map.setCenter(point);
      this._map.panTo(point);
    }
  }

  // 初始化绘图工具
  initDrawingTool() {
    this.drawingTool = new DrawingTool(this._map, {
      drawCallback: this.drawCallback,
    });
  }

  // 初始化设置
  initSettings(settings) {
    this.settings = new MapSettings(settings);
    this.setZoom(this.settings.zoom);
    this.targetDragging(this.settings.dragEnabled);
    this.setCenter(this.settings.center);
  }

  // 管理拖拽
  targetDragging(flag) {
    if (flag === undefined) {
      this.settings.change("dragEnabled", !this.settings.dragEnabled);
    } else {
      this.settings.change("dragEnabled", flag);
    }
  }

  // 确认修改地图中心点
  confirmChangeCenter(isCancel = false) {
    if (!isCancel && this.tempClickPoint) {
      this.setCenter(this.tempClickPoint);
    }

    this._cleanupCenterSelection();
  }

  // 清理中心点选择状态
  _cleanupCenterSelection() {
    if (this.openCenterModalCb) {
      this._map.removeEventListener("click", this.openCenterModalCb);
    }

    if (this.tempCenterPoint) {
      this._map.removeOverlay(this.tempCenterPoint);
    }

    this.isOpenCenterModal = false;
    this.openCenterModalCb = null;
    this.tempClickPoint = null;
    this.tempCenterPoint = null;
  }

  // 标记地图中心点
  markCenterPoint(center) {
    if (this.tempCenterPoint) {
      this.tempCenterPoint.setPosition(center);
      return;
    }

    this.tempCenterPoint = new BMap.Marker(center, {
      id: "default-center-point",
      name: "地图中心点",
    });
    this._map.addOverlay(this.tempCenterPoint);
  }

  // 点击选择地图中心
  _clickSelectCenter({ point }) {
    this.tempClickPoint = point;
    this.markCenterPoint(point);
  }

  // 打开中心点选择模式
  openCenterModal() {
    if (this.isOpenCenterModal) return;

    this._cleanupCenterSelection();
    this.isOpenCenterModal = true;
    this.openCenterModalCb = this._clickSelectCenter.bind(this);
    this._map.addEventListener("click", this.openCenterModalCb);
    this.markCenterPoint(this.settings.center);
  }

  // 设置地图中心
  setCenter(point) {
    if (!(point instanceof BMap.Point)) {
      point = new BMap.Point(point.lng, point.lat);
    }
    this._map.setCenter(point);
    this._map.panTo(point);
    this.settings.change("center", point);
  }

  getCenter() {
    return this._map.getCenter();
  }

  // 设置缩放级别
  setZoom(zoom) {
    this._map.setZoom(zoom);
    this.settings.change("zoom", zoom);
  }

  getZoom() {
    return this._map.getZoom();
  }

  // 导出配置
  exportConfig() {
    return this.settings;
  }

  // 销毁地图
  destroy() {
    this._cleanupCenterSelection();
    eventBus.emit(eventMap.mapDestroy, this);
    this._map = null;
  }
}
