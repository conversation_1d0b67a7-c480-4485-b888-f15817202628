/**
 * 绘图工具类，用于在百度地图上进行标记点、多边形等图形的绘制
 */
export default class DrawingTool {
  /**
   * 创建绘图工具实例
   * @param {BMap.Map} map - 百度地图实例
   * @param {Object} options - 配置选项
   * @param {Object} [options.polygons] - 多边形配置
   * @param {Object} [options.polylines] - 折线配置
   * @param {Object} [options.circles] - 圆形配置
   * @param {Object} [options.rectangles] - 矩形配置
   * @param {Object} [options.markers] - 标记点配置
   * @param {Function} [options.drawCallback] - 绘制完成回调函数
   * @param {string} [options.drawType='marker'] - 默认绘制类型
   */
  constructor(map, options = {}) {
    this._map = map;
    this._drawingManager = null;

    // 绘制图形的样式配置
    this.styleOptions = {
      strokeColor: "#096dd9", // 边线颜色
      fillColor: "#40a9ff7d", // 填充颜色，当参数为空时，圆形将没有填充效果
      strokeWeight: 2, // 边线的宽度，以像素为单位
      strokeOpacity: 0.8, // 边线透明度，取值范围0 - 1
      fillOpacity: 0.6, // 填充的透明度，取值范围0 - 1
      strokeStyle: "solid", // 边线的样式，solid或dashed
    };

    // 初始化各类图形数据
    this.polygons = options.polygons || { pointList: [] };
    this.polylines = options.polylines || {};
    this.circles = options.circles || {};
    this.rectangles = options.rectangles || {};
    this.markers = options.markers || {};

    // 绘制完成的回调函数
    this.drawCallback = options.drawCallback;

    // 默认绘制类型
    this.drawType = options.drawType || "marker";

    // 初始化绘图工具
    this.init();
  }

  /**
   * 初始化绘图管理器
   */
  init() {
    // 创建百度地图绘图管理器实例
    // eslint-disable-next-line no-undef
    this._drawingManager = new BMapLib.DrawingManager(this._map, {
      isOpen: false, // 是否开启绘制模式
      enableDrawingTool: false, // 是否显示工具栏
      drawingToolOptions: {
        // eslint-disable-next-line no-undef
        anchor: BMAP_ANCHOR_TOP_RIGHT, // 工具栏位置
        offset: new BMap.Size(5, 5), // 工具栏偏移量
      },
      // 设置各类图形的样式
      circleOptions: this.styleOptions, // 圆的样式
      polylineOptions: this.styleOptions, // 线的样式
      polygonOptions: this.styleOptions, // 多边形的样式
      rectangleOptions: this.styleOptions, // 矩形的样式
    });

    // 添加覆盖物绘制完成事件
    this._drawingManager &&
      this._drawingManager.addEventListener("overlaycomplete", (n, e) => {
        this.drawOverlayComplete(e);
      });
  }

  /**
   * 处理覆盖物绘制完成事件
   * @param {Object} e - 绘制完成事件对象
   */
  drawOverlayComplete(e) {
    if (!e.drawingMode) return;

    // 立即清除鼠标绘制的临时覆盖物
    this._map && this._map.removeOverlay(e.overlay);
    this.emptyOverlay();

    const overlay = e.overlay;
    const drawPolygonTypeList = ["polygon", "polyline", "circle", "rectangle"];

    // 处理多边形类型的覆盖物
    if (drawPolygonTypeList.includes(e.drawingMode)) {
      const overLayCenterPoint = this.getOverLayCenterPoint(e.overlay.getPath());
      const polygonOverlay = {
        pointList: overlay.getPath().map((p) => [p.lng, p.lat]),
        calculate: e.calculate,
        editing: false,
        centerPoint: overLayCenterPoint,
      };

      // 根据绘制类型设置对应的覆盖物数据
      switch (e.drawingMode) {
        case "polygon":
          this.polygons = polygonOverlay;
          this.drawType = "polygon";
          break;
        case "polyline":
          this.polylines = polygonOverlay;
          this.drawType = "polyline";
          break;
        case "circle":
          this.circles = polygonOverlay;
          this.drawType = "circle";
          break;
        case "rectangle":
          this.rectangles = polygonOverlay;
          this.drawType = "rectangle";
          break;
        default:
          break;
      }
    } else {
      // 处理标记点类型的覆盖物
      const { lat, lng } = overlay.point;
      this.markers = { point: { lat, lng } };
      this.drawType = "marker";
    }

    // 调用绘制完成回调函数
    this.drawCallback(this.drawType, {
      marker: this.markers,
      polygon: this.polygons,
    });
  }

  /**
   * 计算多边形的中心点坐标
   * @param {Array<BMap.Point>} path - 多边形路径点数组
   * @returns {Object} 中心点坐标对象，包含lng和lat属性
   */
  getOverLayCenterPoint(path) {
    let x = 0.0;
    let y = 0.0;

    // 计算所有点的坐标总和
    for (let i = 0; i < path.length; i++) {
      x = x + parseFloat(path[i].lng);
      y = y + parseFloat(path[i].lat);
    }

    // 计算平均值，即为中心点坐标
    x = x / path.length;
    y = y / path.length;

    return {
      lng: x,
      lat: y,
    };
  }

  /**
   * 清空所有覆盖物数据
   */
  emptyOverlay() {
    this.polygons = {};
    this.polylines = {};
    this.circles = {};
    this.rectangles = {};
    this.markers = {};
  }
}
