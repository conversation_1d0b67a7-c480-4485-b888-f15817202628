import BaseCell from "./BaseCell";

export default class Marker extends BaseCell {
  constructor(layer, param) {
    super(layer, param);

    if (param.options.icon) {
      const {
        url,
        size,
        anchor = {
          width: 0,
          height: 0,
        },
        imageOffset = {
          width: 0,
          height: 0,
        },
        infoWindowAnchor = {
          width: 0,
        },
      } = param.options.icon;
      if (url) {
        const _size = new BMap.Size(size.width || 120, size.height || 120);
        const _anchor = new BMap.Size(
          anchor.width || size.width / 2 || 0,
          anchor.height || size.height || 0,
        );

        param.options.icon = new BMap.Icon(url, _size, {
          anchor: _anchor,
          imageOffset: new BMap.Size(imageOffset.width || 0, imageOffset.height || 0),
          imageSize: _size,
          infoWindowAnchor: new BMap.Size(
            infoWindowAnchor.width || 0,
            infoWindowAnchor.height || 0,
          ),
        });
      }
    }

    this._cell = new BMap.Marker(new BMap.Point(param.lng, param.lat), param.options);

    this.initEvent();
  }
}
