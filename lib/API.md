# 地图组件库 API 文档

## 📖 概述

这是一个完整的企业级地图组件库，基于百度地图API构建，提供了从基础地图操作到高级UI组件的全套解决方案。

## 🚀 快速开始

### 1. 引入依赖

```html
<!-- 百度地图API -->
<script src="https://api.map.baidu.com/api?v=3.0&ak=YOUR_API_KEY"></script>
<script src="https://api.map.baidu.com/library/DrawingManager/1.4/src/DrawingManager_min.js"></script>
<link rel="stylesheet" href="https://api.map.baidu.com/library/DrawingManager/1.4/src/DrawingManager_min.css" />

<!-- 组件样式 -->
<link rel="stylesheet" href="./lib/components/components.css">
```

### 2. 基础使用

```javascript
import Map from './lib/model/Map.js';
import { componentManager } from './lib/components/index.js';

// 创建地图
const map = new Map('mapContainer', onDrawComplete, {
    enablePerformanceMonitoring: true,
    enableCaching: true
});

// 设置地图中心
map.centerAndZoom(new BMap.Point(116.404, 39.915), 11);

// 创建UI组件
const toolbar = componentManager.create('toolbar', '#toolbarContainer', {
    onToolClick: (toolId, tool, activeTool) => {
        console.log('工具点击:', toolId);
    }
});
```

## 🗺️ 核心类

### Map 类

主地图类，提供完整的地图功能。

#### 构造函数

```javascript
new Map(container, drawCallback, settings)
```

**参数：**
- `container` (String|Element): 地图容器ID或DOM元素
- `drawCallback` (Function): 绘制完成回调函数
- `settings` (Object): 地图配置选项

**配置选项：**
```javascript
{
    enablePerformanceMonitoring: true,  // 启用性能监控
    enableCaching: true,                 // 启用缓存
    enableErrorHandling: true,           // 启用错误处理
    cacheSize: 50,                      // 缓存大小
    performance: {                       // 性能配置
        // 详细配置...
    }
}
```

#### 主要方法

```javascript
// 地图操作
map.centerAndZoom(point, zoom)          // 设置中心点和缩放级别
map.panTo(point)                        // 平移到指定点
map.setZoom(zoom)                       // 设置缩放级别
map.zoomIn()                           // 放大
map.zoomOut()                          // 缩小

// 覆盖物操作
map.addMarker(point, options)          // 添加标记点
map.addPolygon(points, options)        // 添加多边形
map.addPolyline(points, options)       // 添加折线
map.removeOverlay(overlay)             // 移除覆盖物
map.clearOverlays()                    // 清空所有覆盖物

// 信息窗口
map.openInfoWindow(content, point, options)  // 打开信息窗口
map.closeInfoWindow()                        // 关闭信息窗口

// 省份多边形
map.markProvince(provinceName, options)      // 标记省份
map.clearMarkProvince()                      // 清除省份标记

// 图层管理
map.addLayer(layer)                    // 添加图层
map.removeLayer(layerId)               // 移除图层
map.setLayerVisibility(layerId, visible) // 设置图层可见性

// 数据源管理
map.addDataSource(dataSource)          // 添加数据源
map.removeDataSource(dataSourceId)     // 移除数据源
map.refreshDataSource(dataSourceId)    // 刷新数据源

// 性能监控
map.getPerformanceReport()             // 获取性能报告
map.recordPerformanceMetric(name, value) // 记录性能指标
map.optimizePerformance(options)       // 优化性能

// 生命周期
map.destroy()                          // 销毁地图
```

### DrawingTool 类

绘图工具类，提供完整的绘制功能。

#### 主要方法

```javascript
// 绘制控制
drawingTool.open(drawingMode)          // 开启绘制
drawingTool.close()                    // 关闭绘制
drawingTool.setDrawingMode(mode)       // 设置绘制模式

// 编辑功能
drawingTool.enterEditMode(type, overlayId) // 进入编辑模式
drawingTool.exitEditMode()                 // 退出编辑模式
drawingTool.deleteEditingOverlay()         // 删除正在编辑的覆盖物

// 撤销重做
drawingTool.undo()                     // 撤销
drawingTool.redo()                     // 重做
drawingTool.clearHistory()             // 清空历史

// 样式管理
drawingTool.updateStyle(styleOptions)  // 更新样式
drawingTool.getCurrentStyle()          // 获取当前样式

// 数据管理
drawingTool.getAllOverlays()           // 获取所有覆盖物
drawingTool.exportOverlays(format)     // 导出数据
drawingTool.emptyOverlay()             // 清空覆盖物
```

## 🎨 UI组件

### ComponentManager 类

组件管理器，提供统一的组件管理功能。

```javascript
import { componentManager } from './lib/components/index.js';

// 创建组件
const component = componentManager.create(type, container, options);

// 注册组件
componentManager.register(id, component);

// 获取组件
const component = componentManager.get(id);

// 设置主题
componentManager.setTheme('dark');

// 更新全局配置
componentManager.updateGlobalConfig({ disabled: false });

// 销毁所有组件
componentManager.destroyAll();
```

### ColorPicker 组件

颜色选择器组件。

```javascript
const colorPicker = componentManager.create('colorPicker', '#container', {
    defaultColor: '#1890ff',           // 默认颜色
    presetColors: [...],               // 预设颜色数组
    showPresets: true,                 // 显示预设颜色
    showInput: true,                   // 显示输入框
    showClear: true,                   // 显示清空按钮
    size: 'normal',                    // 尺寸: small, normal, large
    disabled: false,                   // 是否禁用
    onChange: (color) => {             // 颜色变化回调
        console.log('选择颜色:', color);
    },
    onClear: () => {                   // 清空回调
        console.log('颜色已清空');
    }
});

// 方法
colorPicker.setColor('#ff0000');      // 设置颜色
colorPicker.getColor();               // 获取颜色
colorPicker.setDisabled(true);        // 设置禁用状态
```

### IconSelector 组件

图标选择器组件。

```javascript
const iconSelector = componentManager.create('iconSelector', '#container', {
    iconSpritUrl: '/icons/sprite.png', // 图标雪碧图URL
    selectedIcon: null,                // 默认选中图标
    size: 'normal',                    // 尺寸
    showUpload: true,                  // 显示上传功能
    maxCustomIcons: 10,                // 最大自定义图标数
    onChange: (icon) => {              // 图标变化回调
        console.log('选择图标:', icon);
    }
});

// 方法
iconSelector.setSelectedIcon(icon);   // 设置选中图标
iconSelector.getSelectedIcon();       // 获取选中图标
```

### LayerPanel 组件

图层控制面板组件。

```javascript
const layerPanel = componentManager.create('layerPanel', '#container', {
    layerSystem: map.layerSystem,      // 图层系统实例
    showVisibilityToggle: true,        // 显示可见性切换
    showOpacityControl: true,          // 显示透明度控制
    showStyleConfig: true,             // 显示样式配置
    showLayerOrder: true,              // 显示图层排序
    collapsible: true,                 // 可折叠
    defaultCollapsed: false,           // 默认折叠状态
    onLayerChange: (action, layerId, value) => {
        console.log('图层变化:', action, layerId, value);
    }
});
```

### Toolbar 组件

工具栏组件。

```javascript
const toolbar = componentManager.create('toolbar', '#container', {
    orientation: 'horizontal',         // 方向: horizontal, vertical
    position: 'top-left',             // 位置: top-left, top-right, bottom-left, bottom-right
    size: 'normal',                   // 尺寸: small, normal, large
    theme: 'light',                   // 主题: light, dark
    collapsible: true,                // 可折叠
    tools: [...],                     // 工具配置数组
    onToolClick: (toolId, tool, activeTool) => {
        console.log('工具点击:', toolId);
    }
});

// 方法
toolbar.activateTool(toolId);         // 激活工具
toolbar.deactivateTool();             // 取消激活工具
toolbar.addTool(tool, index);         // 添加工具
toolbar.removeTool(toolId);           // 移除工具
toolbar.setToolEnabled(toolId, enabled); // 设置工具状态
```

### TimeSlider 组件

时间滑块组件。

```javascript
const timeSlider = componentManager.create('timeSlider', '#container', {
    startTime: new Date('2024-01-01'), // 开始时间
    endTime: new Date(),               // 结束时间
    currentTime: null,                 // 当前时间
    step: 60 * 60 * 1000,             // 步长（毫秒）
    autoPlay: false,                   // 自动播放
    playSpeed: 1000,                   // 播放速度
    showPlayControls: true,            // 显示播放控制
    showTimeDisplay: true,             // 显示时间显示
    showSpeedControl: true,            // 显示速度控制
    timeFormat: 'YYYY-MM-DD HH:mm',   // 时间格式
    onChange: (time) => {              // 时间变化回调
        console.log('时间变化:', time);
    },
    onPlay: (time) => {                // 播放回调
        console.log('开始播放:', time);
    },
    onPause: (time) => {               // 暂停回调
        console.log('暂停播放:', time);
    }
});

// 方法
timeSlider.setCurrentTime(time);      // 设置当前时间
timeSlider.setTimeRange(start, end);  // 设置时间范围
timeSlider.play();                    // 播放
timeSlider.pause();                   // 暂停
timeSlider.stepForward();             // 向前一步
timeSlider.stepBackward();            // 向后一步
timeSlider.reset();                   // 重置
```

## 🛠️ 工具类

### 性能优化

```javascript
import { 
    performanceMonitor, 
    debounce, 
    throttle, 
    VirtualScrollManager,
    ImageLazyLoader 
} from './lib/utils/PerformanceOptimizer.js';

// 性能监控
performanceMonitor.start();
performanceMonitor.recordMetric('custom.metric', 100);
const report = performanceMonitor.getPerformanceReport();

// 防抖节流
const debouncedFn = debounce(fn, 300);
const throttledFn = throttle(fn, 100);

// 虚拟滚动
const virtualScroll = new VirtualScrollManager({
    itemHeight: 50,
    containerHeight: 400,
    items: data
});

// 图片懒加载
const lazyLoader = new ImageLazyLoader();
lazyLoader.observe(imgElement);
```

### 懒加载

```javascript
import { LazyLoader } from './lib/utils/LazyLoader.js';

// 模块懒加载
const module = await LazyLoader.module('./path/to/module.js');

// 脚本懒加载
await LazyLoader.script('https://cdn.example.com/lib.js');

// 样式懒加载
await LazyLoader.style('https://cdn.example.com/style.css');

// 预加载
await LazyLoader.preload([
    { type: 'script', path: 'lib1.js' },
    { type: 'style', path: 'style1.css' }
]);
```

### 资源管理

```javascript
import { globalResourceManager } from './lib/utils/ResourceManager.js';

// 注册资源
globalResourceManager.register('myResource', resource, cleanupFn);

// 添加事件监听器
const listenerId = globalResourceManager.addEventListener(target, 'click', handler);

// 设置定时器
const timerId = globalResourceManager.setTimeout(callback, 1000);

// 获取统计信息
const stats = globalResourceManager.getStats();

// 检查资源泄漏
const leakCheck = globalResourceManager.checkLeaks();
```

### 错误处理

```javascript
import { errorHandler } from './lib/utils/ErrorHandler.js';

// 记录错误
errorHandler.error('Something went wrong', { context: 'user-action' });
errorHandler.warn('Warning message');
errorHandler.info('Info message');

// 注册恢复策略
errorHandler.registerRecoveryStrategy('NetworkError', (errorInfo) => {
    // 恢复逻辑
});

// 获取错误统计
const stats = errorHandler.getErrorStats();
```

## 📝 事件系统

```javascript
import eventBus from './lib/utils/event-bus.js';

// 监听事件
eventBus.on('map:click', (data) => {
    console.log('地图点击:', data);
});

// 触发事件
eventBus.emit('custom:event', { data: 'value' });

// 移除监听器
eventBus.off('map:click', handler);
```

## 🎯 最佳实践

### 1. 性能优化

```javascript
// 启用性能监控
const map = new Map('container', callback, {
    enablePerformanceMonitoring: true,
    enableCaching: true
});

// 使用防抖节流
const debouncedSearch = debounce(searchFunction, 300);
const throttledScroll = throttle(scrollHandler, 16);

// 懒加载大型组件
const heavyComponent = await LazyLoader.module('./HeavyComponent.js');
```

### 2. 错误处理

```javascript
// 全局错误处理
errorHandler.addLogger((errorInfo) => {
    // 发送到监控系统
    sendToMonitoring(errorInfo);
});

// 使用错误边界
const safeFunction = withErrorBoundary(riskyFunction, {
    fallback: 'default value',
    onError: (error) => console.error(error)
});
```

### 3. 资源管理

```javascript
// 注册资源以防止内存泄漏
globalResourceManager.register('mapInstance', map, (map) => {
    map.destroy();
});

// 页面卸载时清理
window.addEventListener('beforeunload', () => {
    globalResourceManager.clearAll();
});
```

### 4. 组件使用

```javascript
// 统一主题管理
componentManager.setTheme('dark');

// 批量创建组件
const components = componentManager.createBatch([
    { id: 'toolbar', type: 'toolbar', container: '#toolbar', options: {} },
    { id: 'colorPicker', type: 'colorPicker', container: '#color', options: {} }
]);

// 组件销毁
componentManager.destroyAll();
```

## 📚 更多资源

- [完整示例](./example-complete.html) - 查看完整的使用示例
- [测试页面](./test-*.html) - 各个功能模块的测试页面
- [源码](./model/) - 查看源码实现
- [组件库](./components/) - UI组件源码

## 🤝 贡献指南

欢迎提交Issue和Pull Request来改进这个库。在贡献代码前，请确保：

1. 遵循现有的代码风格
2. 添加适当的测试
3. 更新相关文档
4. 确保所有测试通过

## 📄 许可证

MIT License - 详见 LICENSE 文件
