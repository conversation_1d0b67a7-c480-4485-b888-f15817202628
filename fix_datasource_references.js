/**
 * 批量修复DataSourceAdapter中的引用
 */

const fs = require('fs');
const path = require('path');

const filePath = '/Users/<USER>/Desktop/Work/bingo_codespace/tools/map/src/adapters/DataSourceAdapter.js';

// 读取文件内容
let content = fs.readFileSync(filePath, 'utf8');

// 批量替换所有的libDataSourceSystem为libDataSource
content = content.replace(/this\.libDataSourceSystem/g, 'this.libDataSource');

// 修复方法名
content = content.replace(/getLibDataSourceSystem\(\)/g, 'getLibDataSource()');
content = content.replace(/getLibDataSourceSystem\(\) \{/g, 'getLibDataSource() {');

// 写回文件
fs.writeFileSync(filePath, content, 'utf8');

console.log('✅ DataSourceAdapter引用修复完成');
console.log('已将所有libDataSourceSystem替换为libDataSource');
