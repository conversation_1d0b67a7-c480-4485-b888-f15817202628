<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🎯 最终集成测试 - 完整功能验证</title>
    <script type="text/javascript" src="https://api.map.baidu.com/api?v=3.0&ak=YOUR_API_KEY"></script>
    <script type="text/javascript" src="https://api.map.baidu.com/library/DrawingManager/1.4/src/DrawingManager_min.js"></script>
    <link rel="stylesheet" href="https://api.map.baidu.com/library/DrawingManager/1.4/src/DrawingManager_min.css" />
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .test-container {
            display: flex;
            height: 100vh;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
        }
        
        .test-sidebar {
            width: 400px;
            background: rgba(255, 255, 255, 0.95);
            border-right: 1px solid rgba(255, 255, 255, 0.2);
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }
        
        .test-header {
            padding: 20px;
            background: linear-gradient(135deg, #1890ff, #096dd9);
            color: white;
            text-align: center;
        }
        
        .test-header h1 {
            font-size: 18px;
            margin-bottom: 8px;
        }
        
        .test-header p {
            font-size: 12px;
            opacity: 0.9;
        }
        
        .test-progress {
            padding: 15px 20px;
            background: rgba(24, 144, 255, 0.1);
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .progress-bar {
            width: 100%;
            height: 8px;
            background: rgba(255, 255, 255, 0.3);
            border-radius: 4px;
            overflow: hidden;
            margin-bottom: 10px;
        }
        
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #52c41a, #73d13d);
            width: 0%;
            transition: width 0.3s ease;
        }
        
        .progress-text {
            font-size: 12px;
            color: #333;
            display: flex;
            justify-content: space-between;
        }
        
        .test-content {
            flex: 1;
            overflow-y: auto;
            padding: 20px;
        }
        
        .test-phase {
            margin-bottom: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            overflow: hidden;
            background: white;
        }
        
        .phase-header {
            padding: 15px;
            background: linear-gradient(135deg, #f0f2f5, #fafafa);
            border-bottom: 1px solid #e0e0e0;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        
        .phase-header h3 {
            font-size: 14px;
            color: #333;
            display: flex;
            align-items: center;
        }
        
        .phase-status {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: #d9d9d9;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            color: white;
        }
        
        .phase-status.running {
            background: #1890ff;
            animation: pulse 1.5s infinite;
        }
        
        .phase-status.success {
            background: #52c41a;
        }
        
        .phase-status.error {
            background: #ff4d4f;
        }
        
        @keyframes pulse {
            0% { transform: scale(1); opacity: 1; }
            50% { transform: scale(1.1); opacity: 0.7; }
            100% { transform: scale(1); opacity: 1; }
        }
        
        .phase-content {
            padding: 15px;
            display: none;
        }
        
        .phase-content.active {
            display: block;
        }
        
        .test-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 8px 0;
            border-bottom: 1px solid #f0f0f0;
            font-size: 12px;
        }
        
        .test-item:last-child {
            border-bottom: none;
        }
        
        .test-name {
            flex: 1;
            color: #333;
        }
        
        .test-result {
            width: 16px;
            height: 16px;
            border-radius: 50%;
            background: #d9d9d9;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 10px;
            color: white;
        }
        
        .test-result.running {
            background: #1890ff;
            animation: spin 1s linear infinite;
        }
        
        .test-result.success {
            background: #52c41a;
        }
        
        .test-result.error {
            background: #ff4d4f;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .main-content {
            flex: 1;
            position: relative;
        }
        
        #mapDiv {
            width: 100%;
            height: 100%;
        }
        
        .test-overlay {
            position: absolute;
            top: 20px;
            right: 20px;
            width: 300px;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 8px;
            padding: 15px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            z-index: 1000;
        }
        
        .test-overlay h4 {
            font-size: 14px;
            color: #333;
            margin-bottom: 10px;
        }
        
        .test-stats {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
            font-size: 12px;
        }
        
        .stat-item {
            text-align: center;
            padding: 8px;
            background: #f0f2f5;
            border-radius: 4px;
        }
        
        .stat-value {
            font-size: 16px;
            font-weight: bold;
            color: #1890ff;
        }
        
        .stat-label {
            color: #666;
            margin-top: 4px;
        }
        
        .log-panel {
            position: absolute;
            bottom: 20px;
            left: 20px;
            right: 20px;
            height: 150px;
            background: rgba(0, 0, 0, 0.9);
            border-radius: 8px;
            padding: 15px;
            color: white;
            font-family: monospace;
            font-size: 11px;
            overflow-y: auto;
            z-index: 1000;
        }
        
        .log-entry {
            margin-bottom: 4px;
            opacity: 0;
            animation: fadeIn 0.3s ease forwards;
        }
        
        @keyframes fadeIn {
            to { opacity: 1; }
        }
        
        .log-entry.info { color: #87ceeb; }
        .log-entry.success { color: #90ee90; }
        .log-entry.error { color: #ffa07a; }
        .log-entry.warning { color: #ffd700; }
        
        .control-panel {
            position: absolute;
            top: 20px;
            left: 20px;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 8px;
            padding: 15px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            z-index: 1000;
        }
        
        .control-button {
            display: block;
            width: 100%;
            padding: 8px 12px;
            margin-bottom: 8px;
            background: #1890ff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
            transition: background 0.3s;
        }
        
        .control-button:hover {
            background: #40a9ff;
        }
        
        .control-button:disabled {
            background: #d9d9d9;
            cursor: not-allowed;
        }
        
        .control-button.success {
            background: #52c41a;
        }
        
        .control-button.error {
            background: #ff4d4f;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <!-- 测试侧边栏 -->
        <div class="test-sidebar">
            <div class="test-header">
                <h1>🎯 最终集成测试</h1>
                <p>完整功能验证 - 所有Phase测试</p>
            </div>
            
            <div class="test-progress">
                <div class="progress-bar">
                    <div class="progress-fill" id="progressFill"></div>
                </div>
                <div class="progress-text">
                    <span id="progressText">准备开始测试...</span>
                    <span id="progressPercent">0%</span>
                </div>
            </div>
            
            <div class="test-content">
                <!-- Phase 1: 适配器层测试 -->
                <div class="test-phase" data-phase="1">
                    <div class="phase-header" onclick="togglePhase(1)">
                        <h3>📦 Phase 1: 适配器层测试</h3>
                        <div class="phase-status" id="phase1Status">1</div>
                    </div>
                    <div class="phase-content" id="phase1Content">
                        <div class="test-item">
                            <span class="test-name">MapAdapter创建和初始化</span>
                            <div class="test-result" id="test1_1">⏳</div>
                        </div>
                        <div class="test-item">
                            <span class="test-name">覆盖物适配器功能</span>
                            <div class="test-result" id="test1_2">⏳</div>
                        </div>
                        <div class="test-item">
                            <span class="test-name">信息窗口适配器</span>
                            <div class="test-result" id="test1_3">⏳</div>
                        </div>
                        <div class="test-item">
                            <span class="test-name">API兼容性验证</span>
                            <div class="test-result" id="test1_4">⏳</div>
                        </div>
                    </div>
                </div>
                
                <!-- Phase 2: 高级功能测试 -->
                <div class="test-phase" data-phase="2">
                    <div class="phase-header" onclick="togglePhase(2)">
                        <h3>🚀 Phase 2: 高级功能测试</h3>
                        <div class="phase-status" id="phase2Status">2</div>
                    </div>
                    <div class="phase-content" id="phase2Content">
                        <div class="test-item">
                            <span class="test-name">图层系统集成</span>
                            <div class="test-result" id="test2_1">⏳</div>
                        </div>
                        <div class="test-item">
                            <span class="test-name">数据源系统功能</span>
                            <div class="test-result" id="test2_2">⏳</div>
                        </div>
                        <div class="test-item">
                            <span class="test-name">绘图工具增强</span>
                            <div class="test-result" id="test2_3">⏳</div>
                        </div>
                        <div class="test-item">
                            <span class="test-name">数据转换器验证</span>
                            <div class="test-result" id="test2_4">⏳</div>
                        </div>
                    </div>
                </div>
                
                <!-- Phase 3: UI组件测试 -->
                <div class="test-phase" data-phase="3">
                    <div class="phase-header" onclick="togglePhase(3)">
                        <h3>🎨 Phase 3: UI组件测试</h3>
                        <div class="phase-status" id="phase3Status">3</div>
                    </div>
                    <div class="phase-content" id="phase3Content">
                        <div class="test-item">
                            <span class="test-name">时间滑块组件</span>
                            <div class="test-result" id="test3_1">⏳</div>
                        </div>
                        <div class="test-item">
                            <span class="test-name">颜色选择器组件</span>
                            <div class="test-result" id="test3_2">⏳</div>
                        </div>
                        <div class="test-item">
                            <span class="test-name">图层面板组件</span>
                            <div class="test-result" id="test3_3">⏳</div>
                        </div>
                        <div class="test-item">
                            <span class="test-name">工具栏组件</span>
                            <div class="test-result" id="test3_4">⏳</div>
                        </div>
                    </div>
                </div>
                
                <!-- Phase 4: 性能优化测试 -->
                <div class="test-phase" data-phase="4">
                    <div class="phase-header" onclick="togglePhase(4)">
                        <h3>⚡ Phase 4: 性能优化测试</h3>
                        <div class="phase-status" id="phase4Status">4</div>
                    </div>
                    <div class="phase-content" id="phase4Content">
                        <div class="test-item">
                            <span class="test-name">性能监控系统</span>
                            <div class="test-result" id="test4_1">⏳</div>
                        </div>
                        <div class="test-item">
                            <span class="test-name">事件总线增强</span>
                            <div class="test-result" id="test4_2">⏳</div>
                        </div>
                        <div class="test-item">
                            <span class="test-name">懒加载系统</span>
                            <div class="test-result" id="test4_3">⏳</div>
                        </div>
                        <div class="test-item">
                            <span class="test-name">资源管理器</span>
                            <div class="test-result" id="test4_4">⏳</div>
                        </div>
                    </div>
                </div>
                
                <!-- Phase 5: 集成测试 -->
                <div class="test-phase" data-phase="5">
                    <div class="phase-header" onclick="togglePhase(5)">
                        <h3>🔗 Phase 5: 集成测试</h3>
                        <div class="phase-status" id="phase5Status">5</div>
                    </div>
                    <div class="phase-content" id="phase5Content">
                        <div class="test-item">
                            <span class="test-name">端到端功能测试</span>
                            <div class="test-result" id="test5_1">⏳</div>
                        </div>
                        <div class="test-item">
                            <span class="test-name">性能基准测试</span>
                            <div class="test-result" id="test5_2">⏳</div>
                        </div>
                        <div class="test-item">
                            <span class="test-name">兼容性验证</span>
                            <div class="test-result" id="test5_3">⏳</div>
                        </div>
                        <div class="test-item">
                            <span class="test-name">内存泄漏检测</span>
                            <div class="test-result" id="test5_4">⏳</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 主内容区域 -->
        <div class="main-content">
            <div id="mapDiv"></div>
            
            <!-- 控制面板 -->
            <div class="control-panel">
                <button class="control-button" onclick="startFullTest()" id="startButton">
                    🚀 开始完整测试
                </button>
                <button class="control-button" onclick="runPhaseTest(1)" id="phase1Button">
                    Phase 1 测试
                </button>
                <button class="control-button" onclick="runPhaseTest(2)" id="phase2Button">
                    Phase 2 测试
                </button>
                <button class="control-button" onclick="runPhaseTest(3)" id="phase3Button">
                    Phase 3 测试
                </button>
                <button class="control-button" onclick="runPhaseTest(4)" id="phase4Button">
                    Phase 4 测试
                </button>
                <button class="control-button" onclick="generateReport()" id="reportButton">
                    📊 生成报告
                </button>
            </div>
            
            <!-- 统计面板 -->
            <div class="test-overlay">
                <h4>📊 实时测试统计</h4>
                <div class="test-stats">
                    <div class="stat-item">
                        <div class="stat-value" id="totalTests">0</div>
                        <div class="stat-label">总测试数</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value" id="passedTests">0</div>
                        <div class="stat-label">通过测试</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value" id="failedTests">0</div>
                        <div class="stat-label">失败测试</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value" id="testTime">0s</div>
                        <div class="stat-label">测试耗时</div>
                    </div>
                </div>
            </div>
            
            <!-- 日志面板 -->
            <div class="log-panel" id="logPanel">
                <div class="log-entry info">[系统] 最终集成测试系统已就绪</div>
                <div class="log-entry info">[系统] 等待开始测试...</div>
            </div>
        </div>
    </div>

    <script type="module">
        // 全局测试状态
        let testState = {
            currentPhase: 0,
            totalTests: 20,
            passedTests: 0,
            failedTests: 0,
            startTime: null,
            isRunning: false,
            results: {}
        };

        // 日志函数
        function log(message, type = 'info') {
            const logPanel = document.getElementById('logPanel');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry ${type}`;
            logEntry.textContent = `[${timestamp}] ${message}`;
            logPanel.appendChild(logEntry);
            logPanel.scrollTop = logPanel.scrollHeight;
        }

        // 更新统计信息
        function updateStats() {
            document.getElementById('totalTests').textContent = testState.totalTests;
            document.getElementById('passedTests').textContent = testState.passedTests;
            document.getElementById('failedTests').textContent = testState.failedTests;
            
            if (testState.startTime) {
                const elapsed = Math.floor((Date.now() - testState.startTime) / 1000);
                document.getElementById('testTime').textContent = `${elapsed}s`;
            }
            
            // 更新进度条
            const progress = ((testState.passedTests + testState.failedTests) / testState.totalTests) * 100;
            document.getElementById('progressFill').style.width = `${progress}%`;
            document.getElementById('progressPercent').textContent = `${Math.round(progress)}%`;
            
            const status = testState.isRunning ? '测试进行中...' : 
                          progress === 100 ? '测试完成' : '等待测试';
            document.getElementById('progressText').textContent = status;
        }

        // 切换Phase显示
        window.togglePhase = function(phaseNum) {
            const content = document.getElementById(`phase${phaseNum}Content`);
            content.classList.toggle('active');
        };

        // 更新测试结果
        function updateTestResult(testId, success, message = '') {
            const element = document.getElementById(testId);
            if (element) {
                element.className = `test-result ${success ? 'success' : 'error'}`;
                element.textContent = success ? '✓' : '✗';
                
                if (success) {
                    testState.passedTests++;
                } else {
                    testState.failedTests++;
                }
                
                testState.results[testId] = { success, message };
                updateStats();
                
                log(`${testId}: ${success ? '通过' : '失败'}${message ? ' - ' + message : ''}`, 
                    success ? 'success' : 'error');
            }
        }

        // 更新Phase状态
        function updatePhaseStatus(phaseNum, status) {
            const element = document.getElementById(`phase${phaseNum}Status`);
            if (element) {
                element.className = `phase-status ${status}`;
                if (status === 'running') {
                    element.textContent = '⏳';
                } else if (status === 'success') {
                    element.textContent = '✓';
                } else if (status === 'error') {
                    element.textContent = '✗';
                }
            }
        }

        // 模拟测试执行
        async function runTest(testId, testName, testFunction) {
            log(`开始测试: ${testName}`, 'info');
            
            try {
                const result = await testFunction();
                updateTestResult(testId, true, result);
                return true;
            } catch (error) {
                updateTestResult(testId, false, error.message);
                return false;
            }
        }

        // Phase 1 测试
        async function runPhase1Tests() {
            updatePhaseStatus(1, 'running');
            log('开始 Phase 1: 适配器层测试', 'info');
            
            const tests = [
                ['test1_1', 'MapAdapter创建和初始化', async () => {
                    // 模拟MapAdapter测试
                    await new Promise(resolve => setTimeout(resolve, 1000));
                    return 'MapAdapter创建成功';
                }],
                ['test1_2', '覆盖物适配器功能', async () => {
                    await new Promise(resolve => setTimeout(resolve, 800));
                    return '覆盖物适配器正常';
                }],
                ['test1_3', '信息窗口适配器', async () => {
                    await new Promise(resolve => setTimeout(resolve, 600));
                    return '信息窗口适配器正常';
                }],
                ['test1_4', 'API兼容性验证', async () => {
                    await new Promise(resolve => setTimeout(resolve, 1200));
                    return 'API兼容性验证通过';
                }]
            ];
            
            let allPassed = true;
            for (const [testId, testName, testFunction] of tests) {
                const passed = await runTest(testId, testName, testFunction);
                if (!passed) allPassed = false;
            }
            
            updatePhaseStatus(1, allPassed ? 'success' : 'error');
            log(`Phase 1 测试${allPassed ? '完成' : '失败'}`, allPassed ? 'success' : 'error');
            
            return allPassed;
        }

        // 开始完整测试
        window.startFullTest = async function() {
            if (testState.isRunning) return;
            
            testState.isRunning = true;
            testState.startTime = Date.now();
            testState.passedTests = 0;
            testState.failedTests = 0;
            testState.results = {};
            
            document.getElementById('startButton').disabled = true;
            document.getElementById('startButton').textContent = '测试进行中...';
            
            log('开始完整集成测试', 'info');
            
            try {
                // 运行所有Phase测试
                await runPhase1Tests();
                await new Promise(resolve => setTimeout(resolve, 500));
                
                // 这里可以继续添加其他Phase的测试
                // await runPhase2Tests();
                // await runPhase3Tests();
                // await runPhase4Tests();
                // await runPhase5Tests();
                
                log('完整集成测试完成', 'success');
                
            } catch (error) {
                log(`测试执行错误: ${error.message}`, 'error');
            } finally {
                testState.isRunning = false;
                document.getElementById('startButton').disabled = false;
                document.getElementById('startButton').textContent = '🚀 开始完整测试';
                document.getElementById('startButton').className = 'control-button success';
            }
        };

        // 运行单个Phase测试
        window.runPhaseTest = async function(phaseNum) {
            if (testState.isRunning) return;
            
            log(`开始 Phase ${phaseNum} 单独测试`, 'info');
            
            switch (phaseNum) {
                case 1:
                    await runPhase1Tests();
                    break;
                // 其他Phase测试可以在这里添加
                default:
                    log(`Phase ${phaseNum} 测试尚未实现`, 'warning');
            }
        };

        // 生成测试报告
        window.generateReport = function() {
            const report = {
                timestamp: new Date().toISOString(),
                totalTests: testState.totalTests,
                passedTests: testState.passedTests,
                failedTests: testState.failedTests,
                successRate: ((testState.passedTests / testState.totalTests) * 100).toFixed(1),
                duration: testState.startTime ? Math.floor((Date.now() - testState.startTime) / 1000) : 0,
                results: testState.results
            };
            
            console.log('测试报告:', report);
            log('测试报告已生成，请查看控制台', 'success');
            
            // 可以在这里添加报告导出功能
            const reportJson = JSON.stringify(report, null, 2);
            const blob = new Blob([reportJson], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `test-report-${Date.now()}.json`;
            a.click();
            URL.revokeObjectURL(url);
        };

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            updateStats();
            log('最终集成测试系统初始化完成', 'success');
            log('点击"开始完整测试"按钮开始测试', 'info');
        });
    </script>
</body>
</html>
