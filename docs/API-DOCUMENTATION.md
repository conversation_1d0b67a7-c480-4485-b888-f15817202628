# 🗺️ 地图组件库 API 文档

> **版本**: 2.0.0 (lib库增强版)  
> **更新时间**: 2024年12月  
> **兼容性**: 100% 向后兼容原有API

## 📋 目录

- [概述](#概述)
- [快速开始](#快速开始)
- [适配器层 API](#适配器层-api)
- [增强组件 API](#增强组件-api)
- [增强工具函数 API](#增强工具函数-api)
- [迁移指南](#迁移指南)
- [最佳实践](#最佳实践)

## 🎯 概述

本地图组件库基于强大的lib库构建，提供了完整的地图功能和增强特性。通过适配器模式，确保与原有API的100%兼容性，同时提供了现代化的Vue组件和性能优化功能。

### 核心特性

- ✅ **100% API兼容性** - 无需修改现有代码
- ✅ **性能优化** - 基于lib库的性能增强
- ✅ **Vue生态集成** - 完整的Vue组件支持
- ✅ **TypeScript支持** - 完整的类型定义
- ✅ **现代化架构** - 模块化、可扩展设计

### 架构层次

```
┌─────────────────────────────────────┐
│           Vue应用层                  │
├─────────────────────────────────────┤
│         增强组件层                   │
│  TimeSlider | ColorPicker | ...     │
├─────────────────────────────────────┤
│         适配器层                     │
│   MapAdapter | LayerAdapter | ...   │
├─────────────────────────────────────┤
│          lib库层                     │
│    Map | Layer | Drawing | ...      │
├─────────────────────────────────────┤
│        百度地图API                   │
└─────────────────────────────────────┘
```

## 🚀 快速开始

### 安装和导入

```javascript
// 导入适配器（兼容原有API）
import { MapAdapter as Map } from '@/adapters';
import { MarkerAdapter as Marker } from '@/adapters';

// 导入增强组件
import { TimeSliderEnhanced, ColorPickerEnhanced } from '@/components/enhanced';

// 导入增强工具函数
import { EnhancedUtilsPlugin } from '@/utils/enhanced';
```

### 基础使用

```javascript
// 创建地图（与原有API完全兼容）
const map = new Map('mapContainer', drawCallback, settings);

// 添加标记点
const marker = map.addMarker({ lng: 116.404, lat: 39.915 }, {
  title: '北京天安门'
});

// 添加多边形
const polygon = map.addPolygon([
  { lng: 116.403, lat: 39.914 },
  { lng: 116.405, lat: 39.914 },
  { lng: 116.405, lat: 39.916 },
  { lng: 116.403, lat: 39.916 }
], {
  fillColor: '#1890ff',
  fillOpacity: 0.3
});
```

## 📦 适配器层 API

### MapAdapter

地图主类适配器，完全兼容原有`src/model/map.js`的API。

#### 构造函数

```javascript
new MapAdapter(dom, drawCallback, settings)
```

**参数:**
- `dom` (String|Element) - 地图容器ID或DOM元素
- `drawCallback` (Function) - 绘制完成回调函数
- `settings` (Object) - 地图设置选项

**示例:**
```javascript
const map = new MapAdapter('mapDiv', (type, data) => {
  console.log('绘制完成:', type, data);
}, {
  enablePerformanceMonitoring: true,
  enableCaching: true
});
```

#### 核心方法

##### `setCenter(point)`
设置地图中心点

**参数:**
- `point` (Object) - 中心点坐标 `{ lng: Number, lat: Number }`

**示例:**
```javascript
map.setCenter({ lng: 116.404, lat: 39.915 });
```

##### `getCenter()`
获取地图中心点

**返回值:** `{ lng: Number, lat: Number }`

##### `setZoom(zoom)`
设置地图缩放级别

**参数:**
- `zoom` (Number) - 缩放级别 (3-19)

##### `getZoom()`
获取地图缩放级别

**返回值:** `Number`

##### `addMarker(point, options)`
添加标记点

**参数:**
- `point` (Object) - 位置坐标
- `options` (Object) - 标记选项

**返回值:** `MarkerAdapter`

**示例:**
```javascript
const marker = map.addMarker({ lng: 116.404, lat: 39.915 }, {
  title: '标记点',
  icon: 'custom-icon.png'
});
```

##### `addPolygon(points, options)`
添加多边形

**参数:**
- `points` (Array) - 顶点坐标数组
- `options` (Object) - 多边形选项

**返回值:** `PolygonAdapter`

##### `addPolyline(points, options)`
添加折线

**参数:**
- `points` (Array) - 路径点坐标数组
- `options` (Object) - 折线选项

**返回值:** `PolylineAdapter`

##### `openInfoWindow(content, point, options)`
打开信息窗口

**参数:**
- `content` (String) - HTML内容
- `point` (Object) - 显示位置
- `options` (Object) - 窗口选项

##### `closeInfoWindow()`
关闭信息窗口

##### `markProvince(provinceName)`
标记省份

**参数:**
- `provinceName` (String) - 省份名称

##### `clearMarkProvince()`
清除省份标记

##### `clearOverlays()`
清空所有覆盖物

##### `destroy()`
销毁地图实例

#### 增强功能

##### `getPerformanceReport()`
获取性能报告

**返回值:** `Object` - 性能指标对象

##### `optimizePerformance()`
执行性能优化

##### `getLibMap()`
获取lib库地图实例

**返回值:** `LibMap` - lib库地图对象

### MarkerAdapter

标记点适配器，兼容原有Marker API。

#### 构造函数

```javascript
new MarkerAdapter(point, options)
```

#### 核心方法

##### `setPosition(point)`
设置标记位置

##### `getPosition()`
获取标记位置

##### `setTitle(title)`
设置标记标题

##### `getTitle()`
获取标记标题

##### `show()`
显示标记

##### `hide()`
隐藏标记

##### `destroy()`
销毁标记

### PolygonAdapter

多边形适配器，兼容原有Polygon API。

#### 构造函数

```javascript
new PolygonAdapter(points, options)
```

#### 核心方法

##### `setPath(points)`
设置多边形路径

##### `getPath()`
获取多边形路径

##### `setFillColor(color)`
设置填充颜色

##### `setStrokeColor(color)`
设置边框颜色

##### `show()`
显示多边形

##### `hide()`
隐藏多边形

##### `destroy()`
销毁多边形

### LayerSystemAdapter

图层系统适配器，兼容原有图层API。

#### 构造函数

```javascript
new LayerSystemAdapter(map)
```

#### 核心方法

##### `init(config)`
初始化图层系统

**参数:**
- `config` (Object) - 初始化配置

**返回值:** `Promise`

##### `addLayer(layerConfig)`
添加图层

**参数:**
- `layerConfig` (Object) - 图层配置

**返回值:** `Promise<String>` - 图层ID

**示例:**
```javascript
const layerId = await layerSystem.addLayer({
  id: 'markers-layer',
  name: '标记图层',
  type: 'marker',
  visible: true,
  data: {
    type: 'FeatureCollection',
    features: [...]
  }
});
```

##### `removeLayer(layerId)`
移除图层

##### `setLayerVisibility(layerId, visible)`
设置图层可见性

##### `setLayerOpacity(layerId, opacity)`
设置图层透明度

##### `getLayerStatistics(layerId)`
获取图层统计信息

##### `getAllLayers()`
获取所有图层

##### `exportLayerData(layerId, format)`
导出图层数据

##### `importLayerData(data, format)`
导入图层数据

## 🎨 增强组件 API

### TimeSliderEnhanced

时间滑块组件，提供时间轴控制功能。

#### Props

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `startTime` | Date\|String\|Number | 7天前 | 开始时间 |
| `endTime` | Date\|String\|Number | 现在 | 结束时间 |
| `currentTime` | Date\|String\|Number | null | 当前时间 |
| `step` | Number | 3600000 | 时间步长(毫秒) |
| `autoPlay` | Boolean | false | 自动播放 |
| `playSpeed` | Number | 1000 | 播放速度(毫秒) |
| `showPlayControls` | Boolean | true | 显示播放控制 |
| `showTimeDisplay` | Boolean | true | 显示时间显示 |
| `timeFormat` | String | 'YYYY-MM-DD HH:mm' | 时间格式 |
| `size` | String | 'normal' | 尺寸大小 |
| `theme` | String | 'light' | 主题样式 |

#### Events

| 事件 | 参数 | 说明 |
|------|------|------|
| `change` | `(time: Date)` | 时间变化 |
| `play` | `(time: Date)` | 开始播放 |
| `pause` | `(time: Date)` | 暂停播放 |
| `speed-change` | `(speed: Number)` | 速度变化 |

#### Methods

##### `setCurrentTime(time)`
设置当前时间

##### `play()`
开始播放

##### `pause()`
暂停播放

##### `stepForward()`
向前一步

##### `stepBackward()`
向后一步

#### 使用示例

```vue
<template>
  <TimeSliderEnhanced
    v-model="currentTime"
    :start-time="startTime"
    :end-time="endTime"
    :auto-play="true"
    @change="handleTimeChange"
  />
</template>

<script>
import { TimeSliderEnhanced } from '@/components/enhanced';

export default {
  components: { TimeSliderEnhanced },
  data() {
    return {
      currentTime: new Date(),
      startTime: new Date(Date.now() - 24 * 60 * 60 * 1000),
      endTime: new Date()
    };
  },
  methods: {
    handleTimeChange(time) {
      console.log('时间变化:', time);
    }
  }
};
</script>
```

### ColorPickerEnhanced

颜色选择器组件，提供颜色选择功能。

#### Props

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `value` | String | '#1890ff' | 颜色值 |
| `presetColors` | Array | [...] | 预设颜色 |
| `showPresets` | Boolean | true | 显示预设颜色 |
| `showInput` | Boolean | true | 显示输入框 |
| `showAlpha` | Boolean | false | 显示透明度 |
| `format` | String | 'hex' | 颜色格式 |

#### Events

| 事件 | 参数 | 说明 |
|------|------|------|
| `input` | `(color: String)` | 颜色输入 |
| `change` | `(color: String)` | 颜色变化 |
| `clear` | `()` | 清空颜色 |

#### 使用示例

```vue
<template>
  <ColorPickerEnhanced
    v-model="selectedColor"
    :show-presets="true"
    :show-alpha="true"
    @change="handleColorChange"
  />
</template>

<script>
import { ColorPickerEnhanced } from '@/components/enhanced';

export default {
  components: { ColorPickerEnhanced },
  data() {
    return {
      selectedColor: '#1890ff'
    };
  },
  methods: {
    handleColorChange(color) {
      console.log('颜色变化:', color);
    }
  }
};
</script>
```

## ⚡ 增强工具函数 API

### 性能优化

#### PerformanceMonitorMixin

Vue性能监控混入，自动监控组件性能。

```javascript
import { PerformanceMonitorMixin } from '@/utils/enhanced';

export default {
  mixins: [PerformanceMonitorMixin],
  // 自动获得性能监控功能
};
```

#### 防抖节流指令

```vue
<template>
  <!-- 防抖输入 -->
  <input v-debounce:300="handleInput" />
  
  <!-- 节流滚动 -->
  <div v-throttle:100.scroll="handleScroll">
</template>
```

#### 虚拟滚动组件

```vue
<template>
  <VirtualScroll 
    :items="largeDataList" 
    :item-height="50"
    :container-height="400"
  >
    <template #default="{ item, style }">
      <div :style="style">{{ item.name }}</div>
    </template>
  </VirtualScroll>
</template>
```

### 事件总线

#### EventBusMixin

Vue事件总线混入，提供自动清理的事件管理。

```javascript
import { EventBusMixin } from '@/utils/enhanced';

export default {
  mixins: [EventBusMixin],
  mounted() {
    // 自动清理的事件监听
    this.$busOn('user:login', this.handleUserLogin, {
      debounce: 300,
      namespace: 'auth'
    });
  }
};
```

### 懒加载

#### 组件懒加载

```javascript
import { vueComponentLazyLoader } from '@/utils/enhanced';

const LazyComponent = vueComponentLazyLoader.createLazyComponent(
  () => import('@/components/HeavyComponent.vue'),
  {
    loading: LoadingComponent,
    error: ErrorComponent
  }
);
```

#### 图片懒加载

```vue
<template>
  <img v-lazy-load="{ src: imageUrl, placeholder: placeholderUrl }" />
</template>
```

### 资源管理

#### ResourceManagerMixin

Vue资源管理混入，自动管理组件资源。

```javascript
import { ResourceManagerMixin } from '@/utils/enhanced';

export default {
  mixins: [ResourceManagerMixin],
  mounted() {
    // 自动管理事件监听器
    this.$registerEventListener(window, 'resize', this.handleResize);
    
    // 自动管理定时器
    this.$registerTimer(() => {
      this.updateData();
    }, 5000, true);
  }
  // 组件销毁时自动清理所有资源
};
```

## 🔄 迁移指南

### 从原有版本迁移

#### 1. 更新导入语句

```javascript
// 原有导入
import Map from "@/model/map";
import Marker from "@/model/marker";

// 更新为适配器导入
import { MapAdapter as Map } from "@/adapters";
import { MarkerAdapter as Marker } from "@/adapters";
```

#### 2. 无需修改使用代码

所有原有的API调用保持不变：

```javascript
// 这些代码无需修改
const map = new Map('mapDiv', drawCallback, settings);
const marker = map.addMarker(point, options);
map.setCenter(newCenter);
```

#### 3. 可选：使用增强功能

```javascript
// 可选：使用增强功能
const performanceReport = map.getPerformanceReport();
map.optimizePerformance();
```

### 渐进式升级策略

1. **Phase 1**: 替换导入语句，验证基础功能
2. **Phase 2**: 集成增强组件，提升用户体验
3. **Phase 3**: 应用性能优化，提升应用性能
4. **Phase 4**: 使用高级功能，实现业务创新

## 🎯 最佳实践

### 性能优化

```javascript
// 1. 使用性能监控混入
export default {
  mixins: [PerformanceMonitorMixin],
  // ...
};

// 2. 使用防抖节流
methods: {
  handleSearch: debounce(function(query) {
    // 搜索逻辑
  }, 300)
}

// 3. 使用虚拟滚动处理大数据
<VirtualScroll :items="largeList" />
```

### 资源管理

```javascript
// 使用资源管理混入自动清理
export default {
  mixins: [ResourceManagerMixin],
  mounted() {
    // 自动管理的资源
    this.$registerEventListener(window, 'resize', this.handleResize);
    this.$registerTimer(this.updateData, 1000, true);
  }
};
```

### 错误处理

```javascript
// 使用try-catch包装适配器调用
try {
  const map = new MapAdapter('mapDiv', drawCallback, settings);
  // 地图操作
} catch (error) {
  console.error('地图初始化失败:', error);
  // 错误处理逻辑
}
```

### 类型安全

```typescript
// 使用TypeScript获得类型安全
import { MapAdapter, MarkerAdapter } from '@/adapters';

const map: MapAdapter = new MapAdapter('mapDiv', drawCallback, settings);
const marker: MarkerAdapter = map.addMarker(point, options);
```

---

## 📞 支持与反馈

如有问题或建议，请通过以下方式联系：

- 📧 邮箱: <EMAIL>
- 🐛 问题反馈: [GitHub Issues](https://github.com/example/map-lib/issues)
- 📖 更多文档: [完整文档站点](https://docs.example.com)

---

**🎉 感谢使用地图组件库！**

> **最后更新**: 2024年12月
> **文档版本**: 2.0.0
> **API兼容性**: 100% 向后兼容
