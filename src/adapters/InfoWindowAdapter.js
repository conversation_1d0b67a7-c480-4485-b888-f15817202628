/**
 * 信息窗口适配器
 * 保持与原有src/model/info-window.js的API兼容性，内部使用lib库的InfoWindow类
 */

import LibInfoWindow from "../../lib/model/InfoWindow.js";

export default class InfoWindowAdapter {
  constructor(map, options = {}) {
    // 使用lib库的InfoWindow类
    this.libInfoWindow = new LibInfoWindow(map, options);
    
    // 保持原有属性的兼容性
    this.map = map;
    this.options = options;
    this.infoWindow = this.libInfoWindow.infoWindow;
    this.isOpen = false;
    this.currentPosition = null;
    this.currentContent = null;
  }

  /**
   * 打开信息窗口 - 兼容原有API
   */
  open(content, position, options = {}) {
    this.currentContent = content;
    this.currentPosition = position;
    this.isOpen = true;
    
    return this.libInfoWindow.open(content, position, options);
  }

  /**
   * 关闭信息窗口 - 兼容原有API
   */
  close() {
    this.isOpen = false;
    this.currentContent = null;
    this.currentPosition = null;
    
    return this.libInfoWindow.close();
  }

  /**
   * 设置内容 - 兼容原有API
   */
  setContent(content) {
    this.currentContent = content;
    return this.libInfoWindow.setContent(content);
  }

  /**
   * 获取内容 - 兼容原有API
   */
  getContent() {
    return this.libInfoWindow.getContent();
  }

  /**
   * 设置位置 - 兼容原有API
   */
  setPosition(position) {
    this.currentPosition = position;
    return this.libInfoWindow.setPosition(position);
  }

  /**
   * 获取位置 - 兼容原有API
   */
  getPosition() {
    return this.libInfoWindow.getPosition();
  }

  /**
   * 设置标题 - 兼容原有API
   */
  setTitle(title) {
    return this.libInfoWindow.setTitle(title);
  }

  /**
   * 获取标题 - 兼容原有API
   */
  getTitle() {
    return this.libInfoWindow.getTitle();
  }

  /**
   * 更新标题 - 兼容原有API
   */
  updateTitle(title) {
    return this.setTitle(title);
  }

  /**
   * 更新内容 - 兼容原有API
   */
  updateContent(content) {
    return this.setContent(content);
  }

  /**
   * 设置宽度 - 兼容原有API
   */
  setWidth(width) {
    return this.libInfoWindow.setWidth(width);
  }

  /**
   * 获取宽度 - 兼容原有API
   */
  getWidth() {
    return this.libInfoWindow.getWidth();
  }

  /**
   * 设置高度 - 兼容原有API
   */
  setHeight(height) {
    return this.libInfoWindow.setHeight(height);
  }

  /**
   * 获取高度 - 兼容原有API
   */
  getHeight() {
    return this.libInfoWindow.getHeight();
  }

  /**
   * 启用自动平移 - 兼容原有API
   */
  enableAutoPan() {
    return this.libInfoWindow.enableAutoPan();
  }

  /**
   * 禁用自动平移 - 兼容原有API
   */
  disableAutoPan() {
    return this.libInfoWindow.disableAutoPan();
  }

  /**
   * 启用关闭按钮 - 兼容原有API
   */
  enableCloseOnClick() {
    return this.libInfoWindow.enableCloseOnClick();
  }

  /**
   * 禁用关闭按钮 - 兼容原有API
   */
  disableCloseOnClick() {
    return this.libInfoWindow.disableCloseOnClick();
  }

  /**
   * 设置最大宽度 - 兼容原有API
   */
  setMaxWidth(maxWidth) {
    return this.libInfoWindow.setMaxWidth(maxWidth);
  }

  /**
   * 获取最大宽度 - 兼容原有API
   */
  getMaxWidth() {
    return this.libInfoWindow.getMaxWidth();
  }

  /**
   * 添加事件监听器 - 兼容原有API
   */
  addEventListener(event, handler) {
    return this.libInfoWindow.addEventListener(event, handler);
  }

  /**
   * 移除事件监听器 - 兼容原有API
   */
  removeEventListener(event, handler) {
    return this.libInfoWindow.removeEventListener(event, handler);
  }

  /**
   * 检查是否打开 - 兼容原有API
   */
  isOpened() {
    return this.isOpen;
  }

  /**
   * 获取当前状态 - 兼容原有API
   */
  getState() {
    return {
      isOpen: this.isOpen,
      content: this.currentContent,
      position: this.currentPosition,
      title: this.getTitle(),
      width: this.getWidth(),
      height: this.getHeight()
    };
  }

  /**
   * 重置信息窗口 - 兼容原有API
   */
  reset() {
    this.close();
    this.currentContent = null;
    this.currentPosition = null;
    return this.libInfoWindow.reset();
  }

  /**
   * 刷新信息窗口 - 兼容原有API
   */
  refresh() {
    if (this.isOpen && this.currentContent && this.currentPosition) {
      return this.open(this.currentContent, this.currentPosition);
    }
    return false;
  }

  /**
   * 设置样式 - 兼容原有API
   */
  setStyle(style) {
    return this.libInfoWindow.setStyle(style);
  }

  /**
   * 获取样式 - 兼容原有API
   */
  getStyle() {
    return this.libInfoWindow.getStyle();
  }

  /**
   * 设置偏移量 - 兼容原有API
   */
  setOffset(offset) {
    return this.libInfoWindow.setOffset(offset);
  }

  /**
   * 获取偏移量 - 兼容原有API
   */
  getOffset() {
    return this.libInfoWindow.getOffset();
  }

  /**
   * 销毁信息窗口 - 兼容原有API
   */
  destroy() {
    this.close();
    this.currentContent = null;
    this.currentPosition = null;
    this.map = null;
    
    return this.libInfoWindow.destroy();
  }

  /**
   * 获取lib库实例 - 新功能
   */
  getLibInfoWindow() {
    return this.libInfoWindow;
  }

  /**
   * 检查是否为适配器实例 - 工具方法
   */
  isAdapter() {
    return true;
  }

  /**
   * 获取适配器版本 - 工具方法
   */
  getAdapterVersion() {
    return "1.0.0";
  }
}
