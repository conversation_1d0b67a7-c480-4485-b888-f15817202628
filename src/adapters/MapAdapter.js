/**
 * Map适配器
 * 保持与原有src/model/map.js的API兼容性，内部使用lib库的Map类
 */

import LibMap from "../../lib/model/Map.js";
import LibMapSettings from "../../lib/model/MapSettings.js";
import eventBus, { eventMap } from "@/utils/event-bus";
import DrawingToolAdapter from "./DrawingToolAdapter.js";

export default class MapAdapter {
  constructor(dom, drawCallback, settings) {
    // 使用lib库的Map类
    this.libMap = new LibMap(dom, drawCallback, settings);
    
    // 保持原有属性的兼容性
    this._map = this.libMap._map;  // 百度地图实例
    this.settings = this.libMap.settings;  // 地图设置
    this.provincePolygon = this.libMap.provincePolygon;  // 省份多边形
    this.infoWindow = this.libMap.infoWindow;  // 信息窗口
    this._infoWindow = this.libMap._infoWindow;  // 信息窗口实例

    // 创建绘图工具适配器
    this.drawingTool = new DrawingToolAdapter(this._map, {
      onDrawComplete: drawCallback
    });
    
    // 保持原有的临时状态变量
    this.isOpenCenterModal = false;
    this.openCenterModalCb = null;
    this.tempClickPoint = null;
    this.tempCenterPoint = null;
    
    // 绑定原有事件处理
    this._initCompatibilityEvents();
  }

  /**
   * 初始化兼容性事件
   * 确保原有的事件处理逻辑正常工作
   */
  _initCompatibilityEvents() {
    // 监听lib库的事件，转发为原有格式
    eventBus.on(eventMap.mapInit, (map) => {
      // 可以在这里添加原有的初始化逻辑
    });
  }

  // ==================== 原有API兼容方法 ====================

  /**
   * 初始化地图 - 兼容原有API
   */
  initMap() {
    // lib库在构造函数中已经初始化，这里保持兼容性
    return this.libMap._initMap();
  }

  /**
   * 初始化绘图工具 - 兼容原有API
   */
  initDrawingTool() {
    // lib库已经初始化，这里保持兼容性
    return this.libMap.initDrawingTool();
  }

  /**
   * 初始化事件 - 兼容原有API
   */
  initEvent() {
    // lib库已经初始化，这里保持兼容性
    return this.libMap.initEvent();
  }

  /**
   * 初始化设置 - 兼容原有API
   */
  initSettings(settings) {
    this.settings = new LibMapSettings(settings);
    this.setZoom(this.settings.zoom);
    this.targetDragging(this.settings.dragEnabled);
    this.setCenter(this.settings.center);
    
    // 重新初始化省份多边形
    this.provincePolygon = this.libMap.initProvincePolygon();
  }

  /**
   * 管理拖拽 - 兼容原有API
   */
  targetDragging(flag) {
    if (flag === undefined) {
      this.settings.change("dragEnabled", !this.settings.dragEnabled);
    } else {
      this.settings.change("dragEnabled", flag);
    }
    this.settings.dragEnabled ? this._map.enableDragging() : this._map.disableDragging();
  }

  /**
   * 设置地图中心 - 兼容原有API
   */
  setCenter(point) {
    return this.libMap.setCenter(point);
  }

  /**
   * 获取地图中心 - 兼容原有API
   */
  getCenter() {
    return this.libMap.getCenter();
  }

  /**
   * 设置缩放级别 - 兼容原有API
   */
  setZoom(zoom) {
    return this.libMap.setZoom(zoom);
  }

  /**
   * 获取缩放级别 - 兼容原有API
   */
  getZoom() {
    return this.libMap.getZoom();
  }

  /**
   * 标记中心点 - 兼容原有API
   */
  markCenterPoint(point) {
    return this.libMap.markCenterPoint(point);
  }

  /**
   * 清除中心点标记 - 兼容原有API
   */
  clearCenterPoint() {
    return this.libMap.clearCenterPoint();
  }

  /**
   * 点击选择地图中心 - 兼容原有API
   */
  openCenterModal() {
    if (this.isOpenCenterModal) return;
    this.isOpenCenterModal = true;
    this.openCenterModalCb = this._clickSelectCenter.bind(this);
    this._map.addEventListener("click", this.openCenterModalCb);
    this.markCenterPoint(this.settings.center);
  }

  /**
   * 关闭中心选择模式 - 兼容原有API
   */
  closeCenterModal() {
    if (!this.isOpenCenterModal) return;
    this.isOpenCenterModal = false;
    if (this.openCenterModalCb) {
      this._map.removeEventListener("click", this.openCenterModalCb);
      this.openCenterModalCb = null;
    }
    this.clearCenterPoint();
  }

  /**
   * 点击选择中心点处理 - 兼容原有API
   */
  _clickSelectCenter(e) {
    this.tempClickPoint = e.point;
    this.clearCenterPoint();
    this.markCenterPoint(e.point);
  }

  /**
   * 确认中心点选择 - 兼容原有API
   */
  confirmCenterSelection() {
    if (this.tempClickPoint) {
      this.setCenter(this.tempClickPoint);
      this.closeCenterModal();
      return this.tempClickPoint;
    }
    return null;
  }

  /**
   * 取消中心点选择 - 兼容原有API
   */
  cancelCenterSelection() {
    this.tempClickPoint = null;
    this.closeCenterModal();
  }

  // ==================== 信息窗口相关方法 ====================

  /**
   * 打开信息窗口 - 兼容原有API
   */
  openInfoWindow(content, point, options = {}) {
    return this.libMap.openInfoWindow(content, point, options);
  }

  /**
   * 关闭信息窗口 - 兼容原有API
   */
  closeInfoWindow() {
    return this.libMap.closeInfoWindow();
  }

  // ==================== 省份多边形相关方法 ====================

  /**
   * 标记省份 - 兼容原有API
   */
  markProvince(provinceName, options = {}) {
    return this.libMap.markProvince(provinceName, options);
  }

  /**
   * 清除省份标记 - 兼容原有API
   */
  clearMarkProvince() {
    return this.libMap.clearMarkProvince();
  }

  // ==================== 覆盖物相关方法 ====================

  /**
   * 添加标记点 - 兼容原有API
   */
  addMarker(point, options = {}) {
    return this.libMap.addMarker(point, options);
  }

  /**
   * 添加多边形 - 兼容原有API
   */
  addPolygon(points, options = {}) {
    return this.libMap.addPolygon(points, options);
  }

  /**
   * 添加折线 - 兼容原有API
   */
  addPolyline(points, options = {}) {
    return this.libMap.addPolyline(points, options);
  }

  /**
   * 移除覆盖物 - 兼容原有API
   */
  removeOverlay(overlay) {
    return this.libMap.removeOverlay(overlay);
  }

  /**
   * 清空所有覆盖物 - 兼容原有API
   */
  clearOverlays() {
    return this.libMap.clearOverlays();
  }

  // ==================== 新增的lib库功能 ====================

  /**
   * 获取图层系统 - 新功能
   */
  getLayerSystem() {
    return this.libMap.layerSystem;
  }

  /**
   * 获取数据源系统 - 新功能
   */
  getDataSourceSystem() {
    return this.libMap.dataSourceSystem;
  }

  /**
   * 获取性能报告 - 新功能
   */
  getPerformanceReport() {
    return this.libMap.getPerformanceReport();
  }

  /**
   * 优化性能 - 新功能
   */
  optimizePerformance(options = {}) {
    return this.libMap.optimizePerformance(options);
  }

  // ==================== 生命周期方法 ====================

  /**
   * 销毁地图 - 兼容原有API，增强功能
   */
  destroy() {
    // 清理适配器特有的状态
    this.closeCenterModal();
    this.tempClickPoint = null;
    this.tempCenterPoint = null;
    
    // 调用lib库的销毁方法
    return this.libMap.destroy();
  }

  // ==================== 事件相关方法 ====================

  /**
   * 添加事件监听器 - 兼容原有API
   */
  addEventListener(event, handler) {
    return this._map.addEventListener(event, handler);
  }

  /**
   * 移除事件监听器 - 兼容原有API
   */
  removeEventListener(event, handler) {
    return this._map.removeEventListener(event, handler);
  }

  // ==================== 工具方法 ====================

  /**
   * 获取地图实例 - 兼容原有API
   */
  getMap() {
    return this._map;
  }

  /**
   * 获取lib库Map实例 - 新功能
   */
  getLibMap() {
    return this.libMap;
  }

  /**
   * 检查是否为适配器实例 - 工具方法
   */
  isAdapter() {
    return true;
  }

  /**
   * 获取适配器版本 - 工具方法
   */
  getAdapterVersion() {
    return "1.0.0";
  }
}
