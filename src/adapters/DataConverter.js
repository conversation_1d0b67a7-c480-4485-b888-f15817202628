/**
 * 数据转换器
 * 在原有数据格式和lib库数据格式之间进行转换
 */

export class DataConverter {
  constructor() {
    // 字段映射配置
    this.fieldMappings = {
      // 图层配置字段映射
      layer: {
        'id': 'id',
        'name': 'name',
        'type': 'type',
        'visible': 'visible',
        'opacity': 'opacity',
        'zIndex': 'order',
        'data': 'data',
        'style': 'style',
        'options': 'options'
      },
      
      // 样式字段映射
      style: {
        'fillColor': 'fillColor',
        'strokeColor': 'strokeColor',
        'fillOpacity': 'fillOpacity',
        'strokeOpacity': 'strokeOpacity',
        'strokeWeight': 'strokeWeight',
        'strokeStyle': 'strokeStyle'
      },
      
      // 数据字段映射
      data: {
        'features': 'features',
        'properties': 'properties',
        'geometry': 'geometry',
        'coordinates': 'coordinates'
      }
    };
  }

  /**
   * 转换图层配置为lib库格式
   * @param {Object} originalConfig 原有配置
   * @returns {Object} lib库格式配置
   */
  convertLayerConfig(originalConfig) {
    if (!originalConfig) return null;

    const libConfig = {};
    const mapping = this.fieldMappings.layer;

    // 基础字段转换
    for (const [originalField, libField] of Object.entries(mapping)) {
      if (originalConfig.hasOwnProperty(originalField)) {
        libConfig[libField] = originalConfig[originalField];
      }
    }

    // 特殊字段处理
    if (originalConfig.zIndex !== undefined) {
      libConfig.order = originalConfig.zIndex;
    }

    // 样式转换
    if (originalConfig.style) {
      libConfig.style = this.convertLayerStyle(originalConfig.style);
    }

    // 数据转换
    if (originalConfig.data) {
      libConfig.data = this.convertLayerData(originalConfig.data);
    }

    // 选项转换
    if (originalConfig.options) {
      libConfig.options = { ...originalConfig.options };
    }

    return libConfig;
  }

  /**
   * 转换lib库配置为原有格式
   * @param {Object} libConfig lib库配置
   * @returns {Object} 原有格式配置
   */
  convertFromLibConfig(libConfig) {
    if (!libConfig) return null;

    const originalConfig = {};
    const mapping = this.fieldMappings.layer;

    // 反向字段转换
    for (const [originalField, libField] of Object.entries(mapping)) {
      if (libConfig.hasOwnProperty(libField)) {
        originalConfig[originalField] = libConfig[libField];
      }
    }

    // 特殊字段处理
    if (libConfig.order !== undefined) {
      originalConfig.zIndex = libConfig.order;
    }

    // 样式转换
    if (libConfig.style) {
      originalConfig.style = this.convertFromLibStyle(libConfig.style);
    }

    // 数据转换
    if (libConfig.data) {
      originalConfig.data = this.convertFromLibData(libConfig.data);
    }

    return originalConfig;
  }

  /**
   * 转换图层样式为lib库格式
   * @param {Object} originalStyle 原有样式
   * @returns {Object} lib库格式样式
   */
  convertLayerStyle(originalStyle) {
    if (!originalStyle) return null;

    const libStyle = {};
    const mapping = this.fieldMappings.style;

    // 基础样式字段转换
    for (const [originalField, libField] of Object.entries(mapping)) {
      if (originalStyle.hasOwnProperty(originalField)) {
        libStyle[libField] = originalStyle[originalField];
      }
    }

    // 特殊样式处理
    if (originalStyle.color) {
      libStyle.strokeColor = originalStyle.color;
    }

    if (originalStyle.weight) {
      libStyle.strokeWeight = originalStyle.weight;
    }

    if (originalStyle.opacity) {
      libStyle.fillOpacity = originalStyle.opacity;
      libStyle.strokeOpacity = originalStyle.opacity;
    }

    return libStyle;
  }

  /**
   * 转换lib库样式为原有格式
   * @param {Object} libStyle lib库样式
   * @returns {Object} 原有格式样式
   */
  convertFromLibStyle(libStyle) {
    if (!libStyle) return null;

    const originalStyle = {};
    const mapping = this.fieldMappings.style;

    // 反向样式字段转换
    for (const [originalField, libField] of Object.entries(mapping)) {
      if (libStyle.hasOwnProperty(libField)) {
        originalStyle[originalField] = libStyle[libField];
      }
    }

    // 特殊样式处理
    if (libStyle.strokeColor && !originalStyle.color) {
      originalStyle.color = libStyle.strokeColor;
    }

    if (libStyle.strokeWeight && !originalStyle.weight) {
      originalStyle.weight = libStyle.strokeWeight;
    }

    return originalStyle;
  }

  /**
   * 转换图层数据为lib库格式
   * @param {Object} originalData 原有数据
   * @returns {Object} lib库格式数据
   */
  convertLayerData(originalData) {
    if (!originalData) return null;

    // 如果已经是GeoJSON格式，直接返回
    if (originalData.type === 'FeatureCollection' || originalData.type === 'Feature') {
      return originalData;
    }

    // 如果是数组格式，转换为GeoJSON
    if (Array.isArray(originalData)) {
      return {
        type: 'FeatureCollection',
        features: originalData.map(item => this.convertFeature(item))
      };
    }

    // 如果是对象格式，尝试转换
    if (typeof originalData === 'object') {
      // 检查是否有features字段
      if (originalData.features) {
        return {
          type: 'FeatureCollection',
          features: originalData.features.map(item => this.convertFeature(item))
        };
      }

      // 单个要素
      return this.convertFeature(originalData);
    }

    return originalData;
  }

  /**
   * 转换lib库数据为原有格式
   * @param {Object} libData lib库数据
   * @returns {Object} 原有格式数据
   */
  convertFromLibData(libData) {
    if (!libData) return null;

    // 如果是GeoJSON格式，可能需要转换为原有格式
    if (libData.type === 'FeatureCollection') {
      return {
        features: libData.features.map(feature => this.convertFromFeature(feature))
      };
    }

    if (libData.type === 'Feature') {
      return this.convertFromFeature(libData);
    }

    return libData;
  }

  /**
   * 转换单个要素为GeoJSON Feature
   * @param {Object} item 原有要素
   * @returns {Object} GeoJSON Feature
   */
  convertFeature(item) {
    if (!item) return null;

    // 如果已经是GeoJSON Feature，直接返回
    if (item.type === 'Feature') {
      return item;
    }

    // 构建GeoJSON Feature
    const feature = {
      type: 'Feature',
      properties: item.properties || {},
      geometry: null
    };

    // 处理几何数据
    if (item.geometry) {
      feature.geometry = item.geometry;
    } else if (item.coordinates) {
      // 根据坐标数据推断几何类型
      feature.geometry = this.inferGeometry(item.coordinates);
    } else if (item.point) {
      feature.geometry = {
        type: 'Point',
        coordinates: [item.point.lng, item.point.lat]
      };
    } else if (item.points) {
      feature.geometry = {
        type: item.points.length > 2 ? 'Polygon' : 'LineString',
        coordinates: item.points.map(p => [p.lng, p.lat])
      };
    }

    // 添加其他属性到properties
    Object.keys(item).forEach(key => {
      if (!['geometry', 'coordinates', 'point', 'points', 'properties'].includes(key)) {
        feature.properties[key] = item[key];
      }
    });

    return feature;
  }

  /**
   * 转换GeoJSON Feature为原有格式
   * @param {Object} feature GeoJSON Feature
   * @returns {Object} 原有格式要素
   */
  convertFromFeature(feature) {
    if (!feature || feature.type !== 'Feature') return feature;

    const item = {
      ...feature.properties
    };

    // 处理几何数据
    if (feature.geometry) {
      const geometry = feature.geometry;
      
      switch (geometry.type) {
        case 'Point':
          item.point = {
            lng: geometry.coordinates[0],
            lat: geometry.coordinates[1]
          };
          break;
        case 'LineString':
        case 'Polygon':
          const coords = geometry.type === 'Polygon' ? geometry.coordinates[0] : geometry.coordinates;
          item.points = coords.map(coord => ({
            lng: coord[0],
            lat: coord[1]
          }));
          break;
        default:
          item.geometry = geometry;
      }
    }

    return item;
  }

  /**
   * 根据坐标推断几何类型
   * @param {Array} coordinates 坐标数组
   * @returns {Object} GeoJSON Geometry
   */
  inferGeometry(coordinates) {
    if (!Array.isArray(coordinates)) return null;

    if (coordinates.length === 2 && typeof coordinates[0] === 'number') {
      // 单点
      return {
        type: 'Point',
        coordinates: coordinates
      };
    }

    if (Array.isArray(coordinates[0])) {
      // 多点，判断是线还是面
      const isClosed = coordinates.length > 2 && 
        coordinates[0][0] === coordinates[coordinates.length - 1][0] &&
        coordinates[0][1] === coordinates[coordinates.length - 1][1];
      
      return {
        type: isClosed ? 'Polygon' : 'LineString',
        coordinates: isClosed ? [coordinates] : coordinates
      };
    }

    return null;
  }

  /**
   * 转换图层统计信息
   * @param {Object} libStats lib库统计信息
   * @returns {Object} 原有格式统计信息
   */
  convertLayerStatistics(libStats) {
    if (!libStats) return null;

    return {
      featureCount: libStats.featureCount || 0,
      visible: libStats.visible || false,
      opacity: libStats.opacity || 1,
      bounds: libStats.bounds || null,
      lastUpdate: libStats.lastUpdate || null,
      renderTime: libStats.renderTime || 0,
      dataSize: libStats.dataSize || 0
    };
  }

  /**
   * 转换所有图层统计信息
   * @param {Object} libStats lib库统计信息
   * @returns {Object} 原有格式统计信息
   */
  convertAllLayersStatistics(libStats) {
    if (!libStats) return null;

    const result = {
      totalLayers: libStats.totalLayers || 0,
      visibleLayers: libStats.visibleLayers || 0,
      totalFeatures: libStats.totalFeatures || 0,
      layers: {}
    };

    if (libStats.layers) {
      Object.keys(libStats.layers).forEach(layerId => {
        result.layers[layerId] = this.convertLayerStatistics(libStats.layers[layerId]);
      });
    }

    return result;
  }

  /**
   * 转换导出数据格式
   * @param {Object} libData lib库导出数据
   * @param {String} format 格式
   * @returns {Object} 原有格式导出数据
   */
  convertExportData(libData, format) {
    if (format === 'geojson') {
      return libData; // GeoJSON格式保持不变
    }

    return this.convertFromLibData(libData);
  }

  /**
   * 转换导入数据格式
   * @param {Object} data 原有格式导入数据
   * @param {String} format 格式
   * @returns {Object} lib库格式导入数据
   */
  convertImportData(data, format) {
    if (format === 'geojson') {
      return data; // GeoJSON格式保持不变
    }

    return this.convertLayerData(data);
  }

  // ==================== 数据源相关转换方法 ====================

  /**
   * 转换数据源配置为lib库格式
   * @param {Object} originalConfig 原有数据源配置
   * @returns {Object} lib库格式配置
   */
  convertDataSourceConfig(originalConfig) {
    if (!originalConfig) return null;

    const libConfig = {
      id: originalConfig.id,
      name: originalConfig.name || originalConfig.id,
      type: originalConfig.type,
      url: originalConfig.url,
      enabled: originalConfig.enabled !== false,
      cache: originalConfig.cache || {},
      refresh: originalConfig.refresh || {},
      params: originalConfig.params || {},
      headers: originalConfig.headers || {},
      timeout: originalConfig.timeout || 30000
    };

    // 处理特殊配置
    if (originalConfig.staticData) {
      libConfig.staticData = this.convertLayerData(originalConfig.staticData);
    }

    if (originalConfig.dynamicConfig) {
      libConfig.dynamicConfig = { ...originalConfig.dynamicConfig };
    }

    return libConfig;
  }

  /**
   * 转换lib库数据源配置为原有格式
   * @param {Object} libConfig lib库数据源配置
   * @returns {Object} 原有格式配置
   */
  convertFromLibDataSourceConfig(libConfig) {
    if (!libConfig) return null;

    const originalConfig = {
      id: libConfig.id,
      name: libConfig.name,
      type: libConfig.type,
      url: libConfig.url,
      enabled: libConfig.enabled,
      cache: libConfig.cache,
      refresh: libConfig.refresh,
      params: libConfig.params,
      headers: libConfig.headers,
      timeout: libConfig.timeout
    };

    // 处理特殊配置
    if (libConfig.staticData) {
      originalConfig.staticData = this.convertFromLibData(libConfig.staticData);
    }

    if (libConfig.dynamicConfig) {
      originalConfig.dynamicConfig = { ...libConfig.dynamicConfig };
    }

    return originalConfig;
  }

  /**
   * 转换加载参数为lib库格式
   * @param {Object} originalParams 原有加载参数
   * @returns {Object} lib库格式参数
   */
  convertLoadParams(originalParams) {
    if (!originalParams) return {};

    return {
      filters: originalParams.filters || {},
      pagination: originalParams.pagination || {},
      sorting: originalParams.sorting || {},
      fields: originalParams.fields || [],
      format: originalParams.format || 'json',
      cache: originalParams.cache !== false,
      timeout: originalParams.timeout || 30000
    };
  }

  /**
   * 转换数据源统计信息
   * @param {Object} libStats lib库统计信息
   * @returns {Object} 原有格式统计信息
   */
  convertDataSourceStatistics(libStats) {
    if (!libStats) return null;

    return {
      id: libStats.id,
      name: libStats.name,
      type: libStats.type,
      enabled: libStats.enabled,
      lastLoad: libStats.lastLoad,
      loadCount: libStats.loadCount,
      errorCount: libStats.errorCount,
      cacheHits: libStats.cacheHits,
      cacheMisses: libStats.cacheMisses,
      averageLoadTime: libStats.averageLoadTime,
      dataSize: libStats.dataSize,
      recordCount: libStats.recordCount
    };
  }

  /**
   * 转换所有数据源统计信息
   * @param {Object} libStats lib库统计信息
   * @returns {Object} 原有格式统计信息
   */
  convertAllDataSourcesStatistics(libStats) {
    if (!libStats) return null;

    const result = {
      totalDataSources: libStats.totalDataSources || 0,
      enabledDataSources: libStats.enabledDataSources || 0,
      totalLoadCount: libStats.totalLoadCount || 0,
      totalErrorCount: libStats.totalErrorCount || 0,
      totalCacheHits: libStats.totalCacheHits || 0,
      totalCacheMisses: libStats.totalCacheMisses || 0,
      dataSources: {}
    };

    if (libStats.dataSources) {
      Object.keys(libStats.dataSources).forEach(dataSourceId => {
        result.dataSources[dataSourceId] = this.convertDataSourceStatistics(
          libStats.dataSources[dataSourceId]
        );
      });
    }

    return result;
  }

  /**
   * 转换缓存配置为lib库格式
   * @param {Object} originalCacheConfig 原有缓存配置
   * @returns {Object} lib库格式缓存配置
   */
  convertCacheConfig(originalCacheConfig) {
    if (!originalCacheConfig) return {};

    return {
      enabled: originalCacheConfig.enabled !== false,
      ttl: originalCacheConfig.ttl || 300000, // 5分钟
      maxSize: originalCacheConfig.maxSize || 100,
      strategy: originalCacheConfig.strategy || 'lru',
      compression: originalCacheConfig.compression || false
    };
  }

  /**
   * 转换lib库缓存配置为原有格式
   * @param {Object} libCacheConfig lib库缓存配置
   * @returns {Object} 原有格式缓存配置
   */
  convertFromLibCacheConfig(libCacheConfig) {
    if (!libCacheConfig) return {};

    return {
      enabled: libCacheConfig.enabled,
      ttl: libCacheConfig.ttl,
      maxSize: libCacheConfig.maxSize,
      strategy: libCacheConfig.strategy,
      compression: libCacheConfig.compression
    };
  }
}
