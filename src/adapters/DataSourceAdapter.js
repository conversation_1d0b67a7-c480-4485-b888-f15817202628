/**
 * 数据源系统适配器
 * 保持与原有src/model/datasource/的API兼容性，内部使用lib库的DataSourceSystem
 */

import { DataSourceSystem } from "../../lib/model/datasource/index.js";
import { DataConverter } from "./DataConverter.js";

export default class DataSourceAdapter {
  constructor() {
    // 使用lib库的DataSourceSystem
    this.libDataSourceSystem = new DataSourceSystem();
    
    // 保持原有属性的兼容性
    this.dataSources = new Map();
    this.isInitialized = false;
    
    // 数据转换器
    this.dataConverter = new DataConverter();
  }

  /**
   * 初始化数据源系统 - 兼容原有API
   */
  async init(config = {}) {
    try {
      // 转换配置格式为lib库格式
      const libConfig = this.dataConverter.convertDataSourceConfig(config);
      
      // 调用lib库的初始化方法
      await this.libDataSourceSystem.init(libConfig);
      
      this.isInitialized = true;
      return this;
    } catch (error) {
      throw new Error(`数据源系统初始化失败: ${error.message}`);
    }
  }

  /**
   * 添加数据源 - 兼容原有API
   */
  async addDataSource(dataSourceConfig) {
    if (!this.isInitialized) {
      throw new Error('数据源系统未初始化');
    }

    try {
      // 转换数据源配置
      const libConfig = this.dataConverter.convertDataSourceConfig(dataSourceConfig);
      
      // 调用lib库方法
      const libDataSource = await this.libDataSourceSystem.addDataSource(libConfig);
      
      // 保存数据源引用
      this.dataSources.set(dataSourceConfig.id, {
        originalConfig: dataSourceConfig,
        libDataSource: libDataSource,
        adapter: this
      });
      
      return libDataSource;
    } catch (error) {
      throw new Error(`添加数据源失败: ${error.message}`);
    }
  }

  /**
   * 移除数据源 - 兼容原有API
   */
  removeDataSource(dataSourceId) {
    try {
      const result = this.libDataSourceSystem.removeDataSource(dataSourceId);
      this.dataSources.delete(dataSourceId);
      return result;
    } catch (error) {
      throw new Error(`移除数据源失败: ${error.message}`);
    }
  }

  /**
   * 获取数据源 - 兼容原有API
   */
  getDataSource(dataSourceId) {
    const dataSourceInfo = this.dataSources.get(dataSourceId);
    if (dataSourceInfo) {
      return dataSourceInfo.libDataSource;
    }
    return this.libDataSourceSystem.getDataSource(dataSourceId);
  }

  /**
   * 获取所有数据源 - 兼容原有API
   */
  getAllDataSources() {
    return this.libDataSourceSystem.getAllDataSources();
  }

  /**
   * 加载数据 - 兼容原有API
   */
  async loadData(dataSourceId, params = {}) {
    try {
      // 转换参数格式
      const libParams = this.dataConverter.convertLoadParams(params);
      
      // 调用lib库方法
      const libData = await this.libDataSourceSystem.loadData(dataSourceId, libParams);
      
      // 转换返回数据格式
      return this.dataConverter.convertFromLibData(libData);
    } catch (error) {
      throw new Error(`加载数据失败: ${error.message}`);
    }
  }

  /**
   * 刷新数据源 - 兼容原有API
   */
  async refreshDataSource(dataSourceId) {
    try {
      const libData = await this.libDataSourceSystem.refreshDataSource(dataSourceId);
      return this.dataConverter.convertFromLibData(libData);
    } catch (error) {
      throw new Error(`刷新数据源失败: ${error.message}`);
    }
  }

  /**
   * 刷新所有数据源 - 兼容原有API
   */
  async refreshAllDataSources() {
    try {
      const libResults = await this.libDataSourceSystem.refreshAllDataSources();
      
      // 转换结果格式
      const results = {};
      Object.keys(libResults).forEach(dataSourceId => {
        results[dataSourceId] = this.dataConverter.convertFromLibData(libResults[dataSourceId]);
      });
      
      return results;
    } catch (error) {
      throw new Error(`刷新所有数据源失败: ${error.message}`);
    }
  }

  /**
   * 设置数据源状态 - 兼容原有API
   */
  setDataSourceEnabled(dataSourceId, enabled) {
    try {
      return this.libDataSourceSystem.setDataSourceEnabled(dataSourceId, enabled);
    } catch (error) {
      throw new Error(`设置数据源状态失败: ${error.message}`);
    }
  }

  /**
   * 获取数据源状态 - 兼容原有API
   */
  getDataSourceEnabled(dataSourceId) {
    return this.libDataSourceSystem.getDataSourceEnabled(dataSourceId);
  }

  /**
   * 获取数据源统计信息 - 兼容原有API
   */
  getDataSourceStatistics(dataSourceId) {
    const libStats = this.libDataSourceSystem.getDataSourceStatistics(dataSourceId);
    return this.dataConverter.convertDataSourceStatistics(libStats);
  }

  /**
   * 获取所有数据源统计信息 - 兼容原有API
   */
  getAllDataSourcesStatistics() {
    const libStats = this.libDataSourceSystem.getAllDataSourcesStatistics();
    return this.dataConverter.convertAllDataSourcesStatistics(libStats);
  }

  /**
   * 清空数据源缓存 - 兼容原有API
   */
  clearDataSourceCache(dataSourceId) {
    return this.libDataSourceSystem.clearDataSourceCache(dataSourceId);
  }

  /**
   * 清空所有缓存 - 兼容原有API
   */
  clearAllCaches() {
    return this.libDataSourceSystem.clearAllCaches();
  }

  /**
   * 设置缓存配置 - 兼容原有API
   */
  setCacheConfig(dataSourceId, cacheConfig) {
    // 转换缓存配置
    const libCacheConfig = this.dataConverter.convertCacheConfig(cacheConfig);
    return this.libDataSourceSystem.setCacheConfig(dataSourceId, libCacheConfig);
  }

  /**
   * 获取缓存配置 - 兼容原有API
   */
  getCacheConfig(dataSourceId) {
    const libCacheConfig = this.libDataSourceSystem.getCacheConfig(dataSourceId);
    return this.dataConverter.convertFromLibCacheConfig(libCacheConfig);
  }

  /**
   * 添加数据源事件监听器 - 兼容原有API
   */
  addEventListener(dataSourceId, event, handler) {
    return this.libDataSourceSystem.addEventListener(dataSourceId, event, handler);
  }

  /**
   * 移除数据源事件监听器 - 兼容原有API
   */
  removeEventListener(dataSourceId, event, handler) {
    return this.libDataSourceSystem.removeEventListener(dataSourceId, event, handler);
  }

  /**
   * 检查数据源是否存在 - 兼容原有API
   */
  hasDataSource(dataSourceId) {
    return this.libDataSourceSystem.hasDataSource(dataSourceId);
  }

  /**
   * 获取数据源数量 - 兼容原有API
   */
  getDataSourceCount() {
    return this.libDataSourceSystem.getDataSourceCount();
  }

  /**
   * 获取数据源配置 - 兼容原有API
   */
  getDataSourceConfig(dataSourceId) {
    const dataSourceInfo = this.dataSources.get(dataSourceId);
    if (dataSourceInfo) {
      return dataSourceInfo.originalConfig;
    }
    
    const libConfig = this.libDataSourceSystem.getDataSourceConfig(dataSourceId);
    return this.dataConverter.convertFromLibDataSourceConfig(libConfig);
  }

  /**
   * 更新数据源配置 - 兼容原有API
   */
  updateDataSourceConfig(dataSourceId, config) {
    // 更新本地配置
    const dataSourceInfo = this.dataSources.get(dataSourceId);
    if (dataSourceInfo) {
      dataSourceInfo.originalConfig = { ...dataSourceInfo.originalConfig, ...config };
    }
    
    // 转换配置并更新lib库
    const libConfig = this.dataConverter.convertDataSourceConfig(config);
    return this.libDataSourceSystem.updateDataSourceConfig(dataSourceId, libConfig);
  }

  /**
   * 验证数据源配置 - 兼容原有API
   */
  validateDataSourceConfig(config) {
    // 转换配置格式
    const libConfig = this.dataConverter.convertDataSourceConfig(config);
    return this.libDataSourceSystem.validateDataSourceConfig(libConfig);
  }

  /**
   * 测试数据源连接 - 兼容原有API
   */
  async testDataSourceConnection(dataSourceId) {
    try {
      return await this.libDataSourceSystem.testDataSourceConnection(dataSourceId);
    } catch (error) {
      throw new Error(`测试数据源连接失败: ${error.message}`);
    }
  }

  /**
   * 销毁数据源系统 - 兼容原有API
   */
  destroy() {
    this.dataSources.clear();
    this.isInitialized = false;
    
    return this.libDataSourceSystem.destroy();
  }

  /**
   * 获取lib库实例 - 新功能
   */
  getLibDataSourceSystem() {
    return this.libDataSourceSystem;
  }

  /**
   * 检查是否为适配器实例 - 工具方法
   */
  isAdapter() {
    return true;
  }

  /**
   * 获取适配器版本 - 工具方法
   */
  getAdapterVersion() {
    return "1.0.0";
  }

  /**
   * 获取初始化状态 - 兼容原有API
   */
  isReady() {
    return this.isInitialized;
  }
}
