/**
 * 适配器主入口文件
 * 统一导出所有适配器，提供便捷的导入方式
 */

import MapAdapter from './MapAdapter.js';
import { MarkerAdapter, PolygonAdapter, PolylineAdapter } from './OverlayAdapter.js';
import InfoWindowAdapter from './InfoWindowAdapter.js';
import LayerSystemAdapter from './LayerSystemAdapter.js';
import { DataConverter } from './DataConverter.js';

// 导出所有适配器
export {
  MapAdapter,
  MarkerAdapter,
  PolygonAdapter,
  PolylineAdapter,
  InfoWindowAdapter,
  LayerSystemAdapter,
  DataConverter
};

// 默认导出
export default {
  MapAdapter,
  MarkerAdapter,
  PolygonAdapter,
  PolylineAdapter,
  InfoWindowAdapter,
  LayerSystemAdapter,
  DataConverter
};

/**
 * 适配器工厂类
 * 提供统一的适配器创建接口
 */
export class AdapterFactory {
  /**
   * 创建Map适配器
   * @param {String|Element} dom 地图容器
   * @param {Function} drawCallback 绘制回调
   * @param {Object} settings 地图设置
   * @returns {MapAdapter} Map适配器实例
   */
  static createMapAdapter(dom, drawCallback, settings) {
    return new MapAdapter(dom, drawCallback, settings);
  }

  /**
   * 创建Marker适配器
   * @param {Object} point 位置点
   * @param {Object} options 选项
   * @returns {MarkerAdapter} Marker适配器实例
   */
  static createMarkerAdapter(point, options) {
    return new MarkerAdapter(point, options);
  }

  /**
   * 创建Polygon适配器
   * @param {Array} points 点数组
   * @param {Object} options 选项
   * @returns {PolygonAdapter} Polygon适配器实例
   */
  static createPolygonAdapter(points, options) {
    return new PolygonAdapter(points, options);
  }

  /**
   * 创建Polyline适配器
   * @param {Array} points 点数组
   * @param {Object} options 选项
   * @returns {PolylineAdapter} Polyline适配器实例
   */
  static createPolylineAdapter(points, options) {
    return new PolylineAdapter(points, options);
  }

  /**
   * 创建InfoWindow适配器
   * @param {Object} map 地图实例
   * @param {Object} options 选项
   * @returns {InfoWindowAdapter} InfoWindow适配器实例
   */
  static createInfoWindowAdapter(map, options) {
    return new InfoWindowAdapter(map, options);
  }

  /**
   * 创建LayerSystem适配器
   * @param {Object} map 地图实例
   * @returns {LayerSystemAdapter} LayerSystem适配器实例
   */
  static createLayerSystemAdapter(map) {
    return new LayerSystemAdapter(map);
  }

  /**
   * 创建数据转换器
   * @returns {DataConverter} 数据转换器实例
   */
  static createDataConverter() {
    return new DataConverter();
  }
}

/**
 * 适配器管理器
 * 提供适配器的统一管理功能
 */
export class AdapterManager {
  constructor() {
    this.adapters = new Map();
    this.factory = AdapterFactory;
  }

  /**
   * 注册适配器
   * @param {String} id 适配器ID
   * @param {Object} adapter 适配器实例
   */
  register(id, adapter) {
    this.adapters.set(id, adapter);
  }

  /**
   * 获取适配器
   * @param {String} id 适配器ID
   * @returns {Object} 适配器实例
   */
  get(id) {
    return this.adapters.get(id);
  }

  /**
   * 移除适配器
   * @param {String} id 适配器ID
   */
  remove(id) {
    const adapter = this.adapters.get(id);
    if (adapter && adapter.destroy) {
      adapter.destroy();
    }
    this.adapters.delete(id);
  }

  /**
   * 获取所有适配器
   * @returns {Map} 适配器映射
   */
  getAll() {
    return this.adapters;
  }

  /**
   * 清空所有适配器
   */
  clear() {
    for (const [id, adapter] of this.adapters) {
      if (adapter && adapter.destroy) {
        adapter.destroy();
      }
    }
    this.adapters.clear();
  }

  /**
   * 获取适配器统计信息
   * @returns {Object} 统计信息
   */
  getStats() {
    const stats = {
      total: this.adapters.size,
      byType: {}
    };

    for (const adapter of this.adapters.values()) {
      const type = adapter.constructor.name;
      stats.byType[type] = (stats.byType[type] || 0) + 1;
    }

    return stats;
  }
}

// 创建全局适配器管理器实例
export const adapterManager = new AdapterManager();

/**
 * 适配器工具函数
 */
export const AdapterUtils = {
  /**
   * 检查是否为适配器实例
   * @param {Object} obj 对象
   * @returns {Boolean} 是否为适配器
   */
  isAdapter(obj) {
    return obj && typeof obj.isAdapter === 'function' && obj.isAdapter();
  },

  /**
   * 获取适配器版本
   * @param {Object} adapter 适配器实例
   * @returns {String} 版本号
   */
  getAdapterVersion(adapter) {
    if (adapter && typeof adapter.getAdapterVersion === 'function') {
      return adapter.getAdapterVersion();
    }
    return 'unknown';
  },

  /**
   * 获取lib库实例
   * @param {Object} adapter 适配器实例
   * @returns {Object} lib库实例
   */
  getLibInstance(adapter) {
    if (adapter && typeof adapter.getLibMap === 'function') {
      return adapter.getLibMap();
    }
    if (adapter && typeof adapter.getLibMarker === 'function') {
      return adapter.getLibMarker();
    }
    if (adapter && typeof adapter.getLibPolygon === 'function') {
      return adapter.getLibPolygon();
    }
    if (adapter && typeof adapter.getLibPolyline === 'function') {
      return adapter.getLibPolyline();
    }
    if (adapter && typeof adapter.getLibInfoWindow === 'function') {
      return adapter.getLibInfoWindow();
    }
    if (adapter && typeof adapter.getLibLayerSystem === 'function') {
      return adapter.getLibLayerSystem();
    }
    return null;
  },

  /**
   * 批量创建适配器
   * @param {Array} configs 配置数组
   * @returns {Array} 适配器数组
   */
  createBatch(configs) {
    return configs.map(config => {
      const { type, ...params } = config;
      
      switch (type) {
        case 'map':
          return AdapterFactory.createMapAdapter(...params);
        case 'marker':
          return AdapterFactory.createMarkerAdapter(...params);
        case 'polygon':
          return AdapterFactory.createPolygonAdapter(...params);
        case 'polyline':
          return AdapterFactory.createPolylineAdapter(...params);
        case 'infoWindow':
          return AdapterFactory.createInfoWindowAdapter(...params);
        case 'layerSystem':
          return AdapterFactory.createLayerSystemAdapter(...params);
        default:
          throw new Error(`Unknown adapter type: ${type}`);
      }
    });
  },

  /**
   * 适配器兼容性检查
   * @param {Object} adapter 适配器实例
   * @returns {Object} 兼容性检查结果
   */
  checkCompatibility(adapter) {
    const result = {
      isCompatible: true,
      issues: [],
      warnings: []
    };

    if (!this.isAdapter(adapter)) {
      result.isCompatible = false;
      result.issues.push('Not a valid adapter instance');
      return result;
    }

    // 检查必要方法
    const requiredMethods = ['destroy'];
    for (const method of requiredMethods) {
      if (typeof adapter[method] !== 'function') {
        result.issues.push(`Missing required method: ${method}`);
        result.isCompatible = false;
      }
    }

    // 检查版本兼容性
    const version = this.getAdapterVersion(adapter);
    if (version === 'unknown') {
      result.warnings.push('Unknown adapter version');
    }

    return result;
  }
};
