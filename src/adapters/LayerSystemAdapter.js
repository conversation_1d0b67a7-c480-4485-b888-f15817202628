/**
 * 图层系统适配器
 * 保持与原有src/model/layer/index.js的API兼容性，内部使用lib库的LayerManager
 */

import { LayerManager } from "../../lib/model/layer/index.js";
import { DataConverter } from "./DataConverter.js";

export default class LayerSystemAdapter {
  constructor(map) {
    // 使用lib库的LayerManager
    this.libLayerManager = new LayerManager(map);
    
    // 保持原有属性的兼容性
    this.map = map;
    this.layers = new Map();
    this.config = null;
    this.isInitialized = false;
    
    // 数据转换器
    this.dataConverter = new DataConverter();
  }

  /**
   * 初始化图层系统 - 兼容原有API
   */
  async init(config = {}) {
    this.config = config;
    
    // 转换配置格式为lib库格式
    const libConfig = this.dataConverter.convertLayerConfig(config);
    
    // 调用lib库的初始化方法
    await this.libLayerManager.init(libConfig);
    
    this.isInitialized = true;
    return this;
  }

  /**
   * 添加图层 - 兼容原有API
   */
  async addLayer(layerConfig) {
    // 转换图层配置
    const libLayerConfig = this.dataConverter.convertLayerConfig(layerConfig);
    
    // 调用lib库方法
    const libLayer = await this.libLayerManager.addLayer(libLayerConfig);
    
    // 保存图层引用
    this.layers.set(layerConfig.id, {
      originalConfig: layerConfig,
      libLayer: libLayer,
      adapter: this
    });
    
    return layerConfig.id;
  }

  /**
   * 移除图层 - 兼容原有API
   */
  removeLayer(layerId) {
    const result = this.libLayerManager.removeLayer(layerId);
    this.layers.delete(layerId);
    return result;
  }

  /**
   * 获取图层 - 兼容原有API
   */
  getLayer(layerId) {
    const layerInfo = this.layers.get(layerId);
    if (layerInfo) {
      return layerInfo.libLayer;
    }
    return this.libLayerManager.getLayer(layerId);
  }

  /**
   * 获取所有图层 - 兼容原有API
   */
  getAllLayers() {
    return this.libLayerManager.getAllLayers();
  }

  /**
   * 设置图层可见性 - 兼容原有API
   */
  setLayerVisibility(layerId, visible) {
    return this.libLayerManager.setLayerVisibility(layerId, visible);
  }

  /**
   * 获取图层可见性 - 兼容原有API
   */
  getLayerVisibility(layerId) {
    return this.libLayerManager.getLayerVisibility(layerId);
  }

  /**
   * 设置图层透明度 - 兼容原有API
   */
  setLayerOpacity(layerId, opacity) {
    return this.libLayerManager.setLayerOpacity(layerId, opacity);
  }

  /**
   * 获取图层透明度 - 兼容原有API
   */
  getLayerOpacity(layerId) {
    return this.libLayerManager.getLayerOpacity(layerId);
  }

  /**
   * 设置图层顺序 - 兼容原有API
   */
  setLayerOrder(layerId, order) {
    return this.libLayerManager.setLayerOrder(layerId, order);
  }

  /**
   * 获取图层顺序 - 兼容原有API
   */
  getLayerOrder(layerId) {
    return this.libLayerManager.getLayerOrder(layerId);
  }

  /**
   * 重新排序图层 - 兼容原有API
   */
  reorderLayer(layerId, targetLayerId) {
    return this.libLayerManager.reorderLayer(layerId, targetLayerId);
  }

  /**
   * 更新图层数据 - 兼容原有API
   */
  async updateLayerData(layerId, data) {
    // 转换数据格式
    const libData = this.dataConverter.convertLayerData(data);
    
    return await this.libLayerManager.updateLayerData(layerId, libData);
  }

  /**
   * 刷新图层 - 兼容原有API
   */
  async refreshLayer(layerId) {
    return await this.libLayerManager.refreshLayer(layerId);
  }

  /**
   * 刷新所有图层 - 兼容原有API
   */
  async refreshAllLayers() {
    return await this.libLayerManager.refreshAllLayers();
  }

  /**
   * 缩放到图层 - 兼容原有API
   */
  zoomToLayer(layerId) {
    return this.libLayerManager.zoomToLayer(layerId);
  }

  /**
   * 缩放到所有图层 - 兼容原有API
   */
  zoomToAllLayers() {
    return this.libLayerManager.zoomToAllLayers();
  }

  /**
   * 设置图层样式 - 兼容原有API
   */
  setLayerStyle(layerId, style) {
    // 转换样式格式
    const libStyle = this.dataConverter.convertLayerStyle(style);
    
    return this.libLayerManager.setLayerStyle(layerId, libStyle);
  }

  /**
   * 获取图层样式 - 兼容原有API
   */
  getLayerStyle(layerId) {
    const libStyle = this.libLayerManager.getLayerStyle(layerId);
    
    // 转换回原有格式
    return this.dataConverter.convertFromLibStyle(libStyle);
  }

  /**
   * 获取图层统计信息 - 兼容原有API
   */
  getLayerStatistics(layerId) {
    const libStats = this.libLayerManager.getLayerStatistics(layerId);
    
    // 转换统计信息格式
    return this.dataConverter.convertLayerStatistics(libStats);
  }

  /**
   * 获取所有图层统计信息 - 兼容原有API
   */
  getAllLayersStatistics() {
    const libStats = this.libLayerManager.getAllLayersStatistics();
    
    // 转换统计信息格式
    return this.dataConverter.convertAllLayersStatistics(libStats);
  }

  /**
   * 清空所有图层 - 兼容原有API
   */
  clearAllLayers() {
    this.layers.clear();
    return this.libLayerManager.clearAllLayers();
  }

  /**
   * 导出图层数据 - 兼容原有API
   */
  exportLayerData(layerId, format = 'json') {
    const libData = this.libLayerManager.exportLayerData(layerId, format);
    
    // 转换导出数据格式
    return this.dataConverter.convertExportData(libData, format);
  }

  /**
   * 导入图层数据 - 兼容原有API
   */
  async importLayerData(layerId, data, format = 'json') {
    // 转换导入数据格式
    const libData = this.dataConverter.convertImportData(data, format);
    
    return await this.libLayerManager.importLayerData(layerId, libData, format);
  }

  /**
   * 添加图层事件监听器 - 兼容原有API
   */
  addEventListener(layerId, event, handler) {
    return this.libLayerManager.addEventListener(layerId, event, handler);
  }

  /**
   * 移除图层事件监听器 - 兼容原有API
   */
  removeEventListener(layerId, event, handler) {
    return this.libLayerManager.removeEventListener(layerId, event, handler);
  }

  /**
   * 获取图层配置 - 兼容原有API
   */
  getLayerConfig(layerId) {
    const layerInfo = this.layers.get(layerId);
    if (layerInfo) {
      return layerInfo.originalConfig;
    }
    
    const libConfig = this.libLayerManager.getLayerConfig(layerId);
    return this.dataConverter.convertFromLibConfig(libConfig);
  }

  /**
   * 更新图层配置 - 兼容原有API
   */
  updateLayerConfig(layerId, config) {
    // 更新本地配置
    const layerInfo = this.layers.get(layerId);
    if (layerInfo) {
      layerInfo.originalConfig = { ...layerInfo.originalConfig, ...config };
    }
    
    // 转换配置并更新lib库
    const libConfig = this.dataConverter.convertLayerConfig(config);
    return this.libLayerManager.updateLayerConfig(layerId, libConfig);
  }

  /**
   * 检查图层是否存在 - 兼容原有API
   */
  hasLayer(layerId) {
    return this.libLayerManager.hasLayer(layerId);
  }

  /**
   * 获取图层数量 - 兼容原有API
   */
  getLayerCount() {
    return this.libLayerManager.getLayerCount();
  }

  /**
   * 销毁图层系统 - 兼容原有API
   */
  destroy() {
    this.layers.clear();
    this.config = null;
    this.isInitialized = false;
    
    return this.libLayerManager.destroy();
  }

  /**
   * 获取lib库实例 - 新功能
   */
  getLibLayerManager() {
    return this.libLayerManager;
  }

  /**
   * 检查是否为适配器实例 - 工具方法
   */
  isAdapter() {
    return true;
  }

  /**
   * 获取适配器版本 - 工具方法
   */
  getAdapterVersion() {
    return "1.0.0";
  }

  /**
   * 获取初始化状态 - 兼容原有API
   */
  isReady() {
    return this.isInitialized;
  }
}