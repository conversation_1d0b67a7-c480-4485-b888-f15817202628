/**
 * 覆盖物适配器
 * 保持与原有覆盖物类的API兼容性，内部使用lib库的覆盖物类
 */

import LibMarker from "../../lib/model/Marker.js";
import LibPolygon from "../../lib/model/Polygon.js";
import LibPolyline from "../../lib/model/Polyline.js";

/**
 * Marker适配器
 * 兼容原有src/model/marker.js的API
 */
export class MarkerAdapter {
  constructor(point, options = {}) {
    // 使用lib库的Marker类
    this.libMarker = new LibMarker(point, options);
    
    // 保持原有属性的兼容性
    this.point = point;
    this.options = options;
    this.overlay = this.libMarker.overlay;
    this.map = null;
  }

  /**
   * 添加到地图 - 兼容原有API
   */
  addToMap(map) {
    this.map = map;
    return this.libMarker.addToMap(map);
  }

  /**
   * 从地图移除 - 兼容原有API
   */
  removeFromMap() {
    const result = this.libMarker.removeFromMap();
    this.map = null;
    return result;
  }

  /**
   * 设置位置 - 兼容原有API
   */
  setPosition(point) {
    this.point = point;
    return this.libMarker.setPosition(point);
  }

  /**
   * 获取位置 - 兼容原有API
   */
  getPosition() {
    return this.libMarker.getPosition();
  }

  /**
   * 设置图标 - 兼容原有API
   */
  setIcon(icon) {
    return this.libMarker.setIcon(icon);
  }

  /**
   * 获取图标 - 兼容原有API
   */
  getIcon() {
    return this.libMarker.getIcon();
  }

  /**
   * 设置标题 - 兼容原有API
   */
  setTitle(title) {
    return this.libMarker.setTitle(title);
  }

  /**
   * 获取标题 - 兼容原有API
   */
  getTitle() {
    return this.libMarker.getTitle();
  }

  /**
   * 显示 - 兼容原有API
   */
  show() {
    return this.libMarker.show();
  }

  /**
   * 隐藏 - 兼容原有API
   */
  hide() {
    return this.libMarker.hide();
  }

  /**
   * 设置样式 - 兼容原有API
   */
  setStyle(style) {
    return this.libMarker.setStyle(style);
  }

  /**
   * 获取样式 - 兼容原有API
   */
  getStyle() {
    return this.libMarker.getStyle();
  }

  /**
   * 添加事件监听器 - 兼容原有API
   */
  addEventListener(event, handler) {
    return this.libMarker.addEventListener(event, handler);
  }

  /**
   * 移除事件监听器 - 兼容原有API
   */
  removeEventListener(event, handler) {
    return this.libMarker.removeEventListener(event, handler);
  }

  /**
   * 销毁 - 兼容原有API
   */
  destroy() {
    this.removeFromMap();
    return this.libMarker.destroy();
  }

  /**
   * 获取lib库实例 - 新功能
   */
  getLibMarker() {
    return this.libMarker;
  }
}

/**
 * Polygon适配器
 * 兼容原有src/model/polygon.js的API
 */
export class PolygonAdapter {
  constructor(points, options = {}) {
    // 使用lib库的Polygon类
    this.libPolygon = new LibPolygon(points, options);
    
    // 保持原有属性的兼容性
    this.points = points;
    this.options = options;
    this.overlay = this.libPolygon.overlay;
    this.map = null;
  }

  /**
   * 添加到地图 - 兼容原有API
   */
  addToMap(map) {
    this.map = map;
    return this.libPolygon.addToMap(map);
  }

  /**
   * 从地图移除 - 兼容原有API
   */
  removeFromMap() {
    const result = this.libPolygon.removeFromMap();
    this.map = null;
    return result;
  }

  /**
   * 设置路径 - 兼容原有API
   */
  setPath(points) {
    this.points = points;
    return this.libPolygon.setPath(points);
  }

  /**
   * 获取路径 - 兼容原有API
   */
  getPath() {
    return this.libPolygon.getPath();
  }

  /**
   * 设置填充颜色 - 兼容原有API
   */
  setFillColor(color) {
    return this.libPolygon.setFillColor(color);
  }

  /**
   * 获取填充颜色 - 兼容原有API
   */
  getFillColor() {
    return this.libPolygon.getFillColor();
  }

  /**
   * 设置边框颜色 - 兼容原有API
   */
  setStrokeColor(color) {
    return this.libPolygon.setStrokeColor(color);
  }

  /**
   * 获取边框颜色 - 兼容原有API
   */
  getStrokeColor() {
    return this.libPolygon.getStrokeColor();
  }

  /**
   * 设置透明度 - 兼容原有API
   */
  setFillOpacity(opacity) {
    return this.libPolygon.setFillOpacity(opacity);
  }

  /**
   * 获取透明度 - 兼容原有API
   */
  getFillOpacity() {
    return this.libPolygon.getFillOpacity();
  }

  /**
   * 显示 - 兼容原有API
   */
  show() {
    return this.libPolygon.show();
  }

  /**
   * 隐藏 - 兼容原有API
   */
  hide() {
    return this.libPolygon.hide();
  }

  /**
   * 设置样式 - 兼容原有API
   */
  setStyle(style) {
    return this.libPolygon.setStyle(style);
  }

  /**
   * 获取样式 - 兼容原有API
   */
  getStyle() {
    return this.libPolygon.getStyle();
  }

  /**
   * 计算面积 - 兼容原有API
   */
  getArea() {
    return this.libPolygon.getArea();
  }

  /**
   * 计算周长 - 兼容原有API
   */
  getPerimeter() {
    return this.libPolygon.getPerimeter();
  }

  /**
   * 添加事件监听器 - 兼容原有API
   */
  addEventListener(event, handler) {
    return this.libPolygon.addEventListener(event, handler);
  }

  /**
   * 移除事件监听器 - 兼容原有API
   */
  removeEventListener(event, handler) {
    return this.libPolygon.removeEventListener(event, handler);
  }

  /**
   * 销毁 - 兼容原有API
   */
  destroy() {
    this.removeFromMap();
    return this.libPolygon.destroy();
  }

  /**
   * 获取lib库实例 - 新功能
   */
  getLibPolygon() {
    return this.libPolygon;
  }
}

/**
 * Polyline适配器
 * 兼容原有src/model/Polyline.js的API
 */
export class PolylineAdapter {
  constructor(points, options = {}) {
    // 使用lib库的Polyline类
    this.libPolyline = new LibPolyline(points, options);
    
    // 保持原有属性的兼容性
    this.points = points;
    this.options = options;
    this.overlay = this.libPolyline.overlay;
    this.map = null;
  }

  /**
   * 添加到地图 - 兼容原有API
   */
  addToMap(map) {
    this.map = map;
    return this.libPolyline.addToMap(map);
  }

  /**
   * 从地图移除 - 兼容原有API
   */
  removeFromMap() {
    const result = this.libPolyline.removeFromMap();
    this.map = null;
    return result;
  }

  /**
   * 设置路径 - 兼容原有API
   */
  setPath(points) {
    this.points = points;
    return this.libPolyline.setPath(points);
  }

  /**
   * 获取路径 - 兼容原有API
   */
  getPath() {
    return this.libPolyline.getPath();
  }

  /**
   * 设置颜色 - 兼容原有API
   */
  setStrokeColor(color) {
    return this.libPolyline.setStrokeColor(color);
  }

  /**
   * 获取颜色 - 兼容原有API
   */
  getStrokeColor() {
    return this.libPolyline.getStrokeColor();
  }

  /**
   * 设置线宽 - 兼容原有API
   */
  setStrokeWeight(weight) {
    return this.libPolyline.setStrokeWeight(weight);
  }

  /**
   * 获取线宽 - 兼容原有API
   */
  getStrokeWeight() {
    return this.libPolyline.getStrokeWeight();
  }

  /**
   * 设置透明度 - 兼容原有API
   */
  setStrokeOpacity(opacity) {
    return this.libPolyline.setStrokeOpacity(opacity);
  }

  /**
   * 获取透明度 - 兼容原有API
   */
  getStrokeOpacity() {
    return this.libPolyline.getStrokeOpacity();
  }

  /**
   * 显示 - 兼容原有API
   */
  show() {
    return this.libPolyline.show();
  }

  /**
   * 隐藏 - 兼容原有API
   */
  hide() {
    return this.libPolyline.hide();
  }

  /**
   * 设置样式 - 兼容原有API
   */
  setStyle(style) {
    return this.libPolyline.setStyle(style);
  }

  /**
   * 获取样式 - 兼容原有API
   */
  getStyle() {
    return this.libPolyline.getStyle();
  }

  /**
   * 计算长度 - 兼容原有API
   */
  getLength() {
    return this.libPolyline.getLength();
  }

  /**
   * 添加事件监听器 - 兼容原有API
   */
  addEventListener(event, handler) {
    return this.libPolyline.addEventListener(event, handler);
  }

  /**
   * 移除事件监听器 - 兼容原有API
   */
  removeEventListener(event, handler) {
    return this.libPolyline.removeEventListener(event, handler);
  }

  /**
   * 销毁 - 兼容原有API
   */
  destroy() {
    this.removeFromMap();
    return this.libPolyline.destroy();
  }

  /**
   * 获取lib库实例 - 新功能
   */
  getLibPolyline() {
    return this.libPolyline;
  }
}

// 导出适配器类
export default {
  MarkerAdapter,
  PolygonAdapter,
  PolylineAdapter
};
