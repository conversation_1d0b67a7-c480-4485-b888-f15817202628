/**
 * 绘图工具适配器
 * 保持与原有src/model/drawingTool.js的API兼容性，内部使用lib库的DrawingTool类
 */

import LibDrawingTool from "../../lib/model/DrawingTool.js";
import eventBus, { eventMap } from "../../lib/utils/event-bus.js";

export default class DrawingToolAdapter {
  constructor(map, options = {}) {
    // 保持原有属性的兼容性
    this.map = map;
    this.options = options;
    this.isOpen = false;
    this.currentDrawingMode = null;
    this.overlays = [];
    this.isInitialized = false;
    this.initPromise = null;

    // 异步初始化
    this._initializeAsync();
  }

  /**
   * 异步初始化方法
   * @private
   */
  async _initializeAsync() {
    if (this.initPromise) {
      return this.initPromise;
    }

    this.initPromise = this._performInitialization();
    return this.initPromise;
  }

  /**
   * 执行实际的初始化
   * @private
   */
  async _performInitialization() {
    try {
      // 等待地图准备就绪
      await this._waitForMapReady();

      // 创建lib库的DrawingTool实例
      this.libDrawingTool = new LibDrawingTool(this.map, this.options);

      // 等待lib库实例准备就绪
      await this._waitForLibReady();

      // 设置drawingManager引用
      this.drawingManager = this.libDrawingTool.drawingManager;

      // 绑定原有事件处理
      this._initCompatibilityEvents();

      this.isInitialized = true;

      // 触发ready事件
      this._notifyReady();

    } catch (error) {
      console.error('DrawingToolAdapter初始化失败:', error);
      this._handleInitError(error);
      throw error;
    }
  }

  /**
   * 等待地图准备就绪
   * @private
   */
  async _waitForMapReady() {
    return new Promise((resolve) => {
      if (this.map && this.map.getLibMap && this.map.getLibMap()) {
        resolve();
      } else {
        // 等待地图初始化完成
        const checkMap = () => {
          if (this.map && this.map.getLibMap && this.map.getLibMap()) {
            resolve();
          } else {
            setTimeout(checkMap, 100);
          }
        };
        checkMap();
      }
    });
  }

  /**
   * 等待lib库实例准备就绪
   * @private
   */
  async _waitForLibReady() {
    return new Promise((resolve) => {
      if (this.libDrawingTool && this.libDrawingTool.drawingManager) {
        resolve();
      } else {
        // 等待lib库实例初始化完成
        const checkLib = () => {
          if (this.libDrawingTool && this.libDrawingTool.drawingManager) {
            resolve();
          } else {
            setTimeout(checkLib, 50);
          }
        };
        checkLib();
      }
    });
  }

  /**
   * 通知初始化完成
   * @private
   */
  _notifyReady() {
    if (this.options.onReady && typeof this.options.onReady === 'function') {
      this.options.onReady(this);
    }
  }

  /**
   * 处理初始化错误
   * @private
   */
  _handleInitError(error) {
    if (this.options.onError && typeof this.options.onError === 'function') {
      this.options.onError(error);
    }
  }

  /**
   * 等待初始化完成
   * @returns {Promise}
   */
  async waitForReady() {
    if (this.isInitialized) {
      return this;
    }

    if (this.initPromise) {
      await this.initPromise;
      return this;
    }

    throw new Error('DrawingToolAdapter未开始初始化');
  }

  /**
   * 初始化兼容性事件
   */
  _initCompatibilityEvents() {
    // 监听绘制完成事件 - 使用事件总线而不是直接的addEventListener
    eventBus.on(eventMap.drawingComplete, (data) => {
      if (data.overlay) {
        this.overlays.push(data.overlay);
      }
      // 触发原有格式的事件
      if (this.options.onDrawComplete) {
        this.options.onDrawComplete(data.drawingMode, data.overlay);
      }
    });

    // 监听绘制开始事件
    eventBus.on(eventMap.drawingStart, (data) => {
      console.log('绘制开始:', data.drawingMode);
    });

    // 监听编辑模式事件
    eventBus.on(eventMap.editModeStart, (data) => {
      console.log('编辑模式开始:', data.type);
    });

    eventBus.on(eventMap.editModeEnd, (data) => {
      console.log('编辑模式结束');
    });

    // 监听撤销重做事件
    eventBus.on(eventMap.drawingUndo, (data) => {
      console.log('撤销操作');
    });

    eventBus.on(eventMap.drawingRedo, (data) => {
      console.log('重做操作');
    });
  }

  /**
   * 开启绘制模式 - 兼容原有API
   */
  async open(drawingMode) {
    try {
      // 确保初始化完成
      await this.waitForReady();

      this.libDrawingTool.open(drawingMode);
      this.isOpen = true;
      this.currentDrawingMode = drawingMode;
      return true;
    } catch (error) {
      throw new Error(`开启绘制模式失败: ${error.message}`);
    }
  }

  /**
   * 关闭绘制模式 - 兼容原有API
   */
  close() {
    try {
      this.libDrawingTool.close();
      this.isOpen = false;
      this.currentDrawingMode = null;
      return true;
    } catch (error) {
      throw new Error(`关闭绘制模式失败: ${error.message}`);
    }
  }

  /**
   * 设置绘制模式 - 兼容原有API
   */
  setDrawingMode(drawingMode) {
    try {
      this.libDrawingTool.setDrawingMode(drawingMode);
      this.currentDrawingMode = drawingMode;
      return true;
    } catch (error) {
      throw new Error(`设置绘制模式失败: ${error.message}`);
    }
  }

  /**
   * 获取当前绘制模式 - 兼容原有API
   */
  getDrawingMode() {
    return this.currentDrawingMode;
  }

  /**
   * 检查是否正在绘制 - 兼容原有API
   */
  isDrawing() {
    return this.isOpen;
  }

  /**
   * 进入编辑模式 - 兼容原有API
   */
  enterEditMode(type, overlayId) {
    try {
      return this.libDrawingTool.enterEditMode(type, overlayId);
    } catch (error) {
      throw new Error(`进入编辑模式失败: ${error.message}`);
    }
  }

  /**
   * 退出编辑模式 - 兼容原有API
   */
  exitEditMode() {
    try {
      return this.libDrawingTool.exitEditMode();
    } catch (error) {
      throw new Error(`退出编辑模式失败: ${error.message}`);
    }
  }

  /**
   * 删除正在编辑的覆盖物 - 兼容原有API
   */
  deleteEditingOverlay() {
    try {
      const result = this.libDrawingTool.deleteEditingOverlay();
      // 从本地覆盖物列表中移除
      this.overlays = this.overlays.filter(overlay => overlay !== result);
      return result;
    } catch (error) {
      throw new Error(`删除编辑覆盖物失败: ${error.message}`);
    }
  }

  /**
   * 撤销操作 - 兼容原有API
   */
  undo() {
    try {
      return this.libDrawingTool.undo();
    } catch (error) {
      throw new Error(`撤销操作失败: ${error.message}`);
    }
  }

  /**
   * 重做操作 - 兼容原有API
   */
  redo() {
    try {
      return this.libDrawingTool.redo();
    } catch (error) {
      throw new Error(`重做操作失败: ${error.message}`);
    }
  }

  /**
   * 清空历史记录 - 兼容原有API
   */
  clearHistory() {
    try {
      return this.libDrawingTool.clearHistory();
    } catch (error) {
      throw new Error(`清空历史记录失败: ${error.message}`);
    }
  }

  /**
   * 检查是否可以撤销 - 兼容原有API
   */
  canUndo() {
    return this.libDrawingTool.canUndo();
  }

  /**
   * 检查是否可以重做 - 兼容原有API
   */
  canRedo() {
    return this.libDrawingTool.canRedo();
  }

  /**
   * 获取历史记录数量 - 兼容原有API
   */
  getHistoryCount() {
    return this.libDrawingTool.getHistoryCount();
  }

  /**
   * 更新绘制样式 - 兼容原有API
   */
  updateStyle(styleOptions) {
    try {
      return this.libDrawingTool.updateStyle(styleOptions);
    } catch (error) {
      throw new Error(`更新绘制样式失败: ${error.message}`);
    }
  }

  /**
   * 获取当前样式 - 兼容原有API
   */
  getCurrentStyle() {
    return this.libDrawingTool.getCurrentStyle();
  }

  /**
   * 设置默认样式 - 兼容原有API
   */
  setDefaultStyle(styleOptions) {
    try {
      return this.libDrawingTool.setDefaultStyle(styleOptions);
    } catch (error) {
      throw new Error(`设置默认样式失败: ${error.message}`);
    }
  }

  /**
   * 获取默认样式 - 兼容原有API
   */
  getDefaultStyle() {
    return this.libDrawingTool.getDefaultStyle();
  }

  /**
   * 获取所有覆盖物 - 兼容原有API
   */
  getAllOverlays() {
    return this.libDrawingTool.getAllOverlays();
  }

  /**
   * 清空所有覆盖物 - 兼容原有API
   */
  emptyOverlay() {
    try {
      const result = this.libDrawingTool.emptyOverlay();
      this.overlays = [];
      return result;
    } catch (error) {
      throw new Error(`清空覆盖物失败: ${error.message}`);
    }
  }

  /**
   * 导出覆盖物数据 - 兼容原有API
   */
  exportOverlays(format = 'json') {
    try {
      return this.libDrawingTool.exportOverlays(format);
    } catch (error) {
      throw new Error(`导出覆盖物数据失败: ${error.message}`);
    }
  }

  /**
   * 导入覆盖物数据 - 兼容原有API
   */
  importOverlays(data, format = 'json') {
    try {
      const result = this.libDrawingTool.importOverlays(data, format);
      // 更新本地覆盖物列表
      this.overlays = this.getAllOverlays();
      return result;
    } catch (error) {
      throw new Error(`导入覆盖物数据失败: ${error.message}`);
    }
  }

  /**
   * 计算覆盖物面积 - 兼容原有API
   */
  calculateArea(overlay) {
    try {
      return this.libDrawingTool.calculateArea(overlay);
    } catch (error) {
      throw new Error(`计算面积失败: ${error.message}`);
    }
  }

  /**
   * 计算覆盖物周长/长度 - 兼容原有API
   */
  calculatePerimeter(overlay) {
    try {
      return this.libDrawingTool.calculatePerimeter(overlay);
    } catch (error) {
      throw new Error(`计算周长失败: ${error.message}`);
    }
  }

  /**
   * 启用测量模式 - 兼容原有API
   */
  enableMeasurement(enabled = true) {
    try {
      return this.libDrawingTool.enableMeasurement(enabled);
    } catch (error) {
      throw new Error(`启用测量模式失败: ${error.message}`);
    }
  }

  /**
   * 检查测量模式是否启用 - 兼容原有API
   */
  isMeasurementEnabled() {
    return this.libDrawingTool.isMeasurementEnabled();
  }

  /**
   * 添加事件监听器 - 兼容原有API
   */
  addEventListener(event, handler) {
    return this.libDrawingTool.addEventListener(event, handler);
  }

  /**
   * 移除事件监听器 - 兼容原有API
   */
  removeEventListener(event, handler) {
    return this.libDrawingTool.removeEventListener(event, handler);
  }

  /**
   * 获取绘制管理器实例 - 兼容原有API
   */
  getDrawingManager() {
    return this.drawingManager;
  }

  /**
   * 设置绘制选项 - 兼容原有API
   */
  setDrawingOptions(options) {
    try {
      return this.libDrawingTool.setDrawingOptions(options);
    } catch (error) {
      throw new Error(`设置绘制选项失败: ${error.message}`);
    }
  }

  /**
   * 获取绘制选项 - 兼容原有API
   */
  getDrawingOptions() {
    return this.libDrawingTool.getDrawingOptions();
  }

  /**
   * 销毁绘图工具 - 兼容原有API
   */
  destroy() {
    this.close();
    this.overlays = [];
    this.map = null;
    this.options = null;
    
    return this.libDrawingTool.destroy();
  }

  /**
   * 获取lib库实例 - 新功能
   */
  getLibDrawingTool() {
    return this.libDrawingTool;
  }

  /**
   * 检查是否为适配器实例 - 工具方法
   */
  isAdapter() {
    return true;
  }

  /**
   * 获取适配器版本 - 工具方法
   */
  getAdapterVersion() {
    return "1.0.0";
  }
}
