/**
 * 增强懒加载系统
 * 基于lib库的懒加载功能，提供Vue项目的懒加载解决方案
 */

import { LazyLoader } from '../../lib/utils/LazyLoader.js';

/**
 * Vue组件懒加载器
 * 支持组件的按需加载
 */
export class VueComponentLazyLoader {
  constructor(options = {}) {
    this.libLazyLoader = new LazyLoader(options);
    this.componentCache = new Map();
    this.loadingComponents = new Set();
    this.errorComponents = new Set();
    this.retryCount = options.retryCount || 3;
    this.retryDelay = options.retryDelay || 1000;
  }

  /**
   * 创建懒加载组件
   * @param {Function} importFunction 动态导入函数
   * @param {Object} options 选项
   */
  createLazyComponent(importFunction, options = {}) {
    return () => ({
      component: this.loadComponent(importFunction, options),
      loading: options.loading || this.createLoadingComponent(),
      error: options.error || this.createErrorComponent(),
      delay: options.delay || 200,
      timeout: options.timeout || 10000
    });
  }

  /**
   * 加载组件
   * @param {Function} importFunction 动态导入函数
   * @param {Object} options 选项
   */
  async loadComponent(importFunction, options = {}) {
    const componentKey = importFunction.toString();
    
    // 检查缓存
    if (this.componentCache.has(componentKey)) {
      return this.componentCache.get(componentKey);
    }
    
    // 检查是否正在加载
    if (this.loadingComponents.has(componentKey)) {
      return new Promise((resolve, reject) => {
        const checkLoading = () => {
          if (this.componentCache.has(componentKey)) {
            resolve(this.componentCache.get(componentKey));
          } else if (this.errorComponents.has(componentKey)) {
            reject(new Error('Component loading failed'));
          } else {
            setTimeout(checkLoading, 100);
          }
        };
        checkLoading();
      });
    }
    
    this.loadingComponents.add(componentKey);
    
    try {
      // 使用lib库的懒加载功能
      const component = await this.libLazyLoader.load(importFunction, options);
      
      // 缓存组件
      this.componentCache.set(componentKey, component);
      this.loadingComponents.delete(componentKey);
      
      return component;
    } catch (error) {
      this.loadingComponents.delete(componentKey);
      this.errorComponents.add(componentKey);
      
      // 重试机制
      if (options.retry !== false && this.retryCount > 0) {
        await this.delay(this.retryDelay);
        return this.retryLoadComponent(importFunction, options);
      }
      
      throw error;
    }
  }

  /**
   * 重试加载组件
   * @param {Function} importFunction 动态导入函数
   * @param {Object} options 选项
   */
  async retryLoadComponent(importFunction, options = {}) {
    const componentKey = importFunction.toString();
    this.errorComponents.delete(componentKey);
    
    const retryOptions = {
      ...options,
      retryCount: (options.retryCount || this.retryCount) - 1
    };
    
    return this.loadComponent(importFunction, retryOptions);
  }

  /**
   * 预加载组件
   * @param {Array} importFunctions 动态导入函数数组
   */
  async preloadComponents(importFunctions) {
    const promises = importFunctions.map(importFunction => 
      this.loadComponent(importFunction, { preload: true })
    );
    
    return Promise.allSettled(promises);
  }

  /**
   * 创建加载中组件
   */
  createLoadingComponent() {
    return {
      template: `
        <div class="lazy-loading">
          <div class="loading-spinner"></div>
          <div class="loading-text">加载中...</div>
        </div>
      `,
      style: `
        .lazy-loading {
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          padding: 20px;
          color: #666;
        }
        .loading-spinner {
          width: 20px;
          height: 20px;
          border: 2px solid #f3f3f3;
          border-top: 2px solid #1890ff;
          border-radius: 50%;
          animation: spin 1s linear infinite;
        }
        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }
        .loading-text {
          margin-top: 10px;
          font-size: 14px;
        }
      `
    };
  }

  /**
   * 创建错误组件
   */
  createErrorComponent() {
    return {
      template: `
        <div class="lazy-error">
          <div class="error-icon">⚠️</div>
          <div class="error-text">组件加载失败</div>
          <button class="retry-button" @click="retry">重试</button>
        </div>
      `,
      methods: {
        retry() {
          this.$emit('retry');
        }
      },
      style: `
        .lazy-error {
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          padding: 20px;
          color: #ff4d4f;
        }
        .error-icon {
          font-size: 24px;
          margin-bottom: 10px;
        }
        .error-text {
          margin-bottom: 10px;
          font-size: 14px;
        }
        .retry-button {
          padding: 4px 8px;
          background: #1890ff;
          color: white;
          border: none;
          border-radius: 4px;
          cursor: pointer;
        }
      `
    };
  }

  /**
   * 延迟函数
   * @param {Number} ms 延迟毫秒数
   */
  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * 清空缓存
   */
  clearCache() {
    this.componentCache.clear();
    this.loadingComponents.clear();
    this.errorComponents.clear();
  }

  /**
   * 获取缓存统计
   */
  getCacheStats() {
    return {
      cached: this.componentCache.size,
      loading: this.loadingComponents.size,
      error: this.errorComponents.size
    };
  }
}

/**
 * Vue路由懒加载器
 * 支持路由组件的按需加载
 */
export class VueRouteLazyLoader extends VueComponentLazyLoader {
  constructor(options = {}) {
    super(options);
    this.routeCache = new Map();
  }

  /**
   * 创建懒加载路由
   * @param {Function} importFunction 动态导入函数
   * @param {Object} options 选项
   */
  createLazyRoute(importFunction, options = {}) {
    return {
      path: options.path,
      name: options.name,
      component: this.createLazyComponent(importFunction, options),
      meta: {
        ...options.meta,
        lazy: true
      }
    };
  }

  /**
   * 批量创建懒加载路由
   * @param {Array} routes 路由配置数组
   */
  createLazyRoutes(routes) {
    return routes.map(route => {
      if (route.component && typeof route.component === 'function') {
        return this.createLazyRoute(route.component, route);
      }
      return route;
    });
  }

  /**
   * 预加载路由组件
   * @param {Array} routeNames 路由名称数组
   */
  async preloadRoutes(routeNames) {
    const importFunctions = routeNames
      .map(name => this.routeCache.get(name))
      .filter(Boolean);
    
    return this.preloadComponents(importFunctions);
  }
}

/**
 * Vue资源懒加载器
 * 支持图片、CSS、JS等资源的懒加载
 */
export class VueResourceLazyLoader {
  constructor(options = {}) {
    this.libLazyLoader = new LazyLoader(options);
    this.resourceCache = new Map();
    this.loadingResources = new Set();
  }

  /**
   * 懒加载图片
   * @param {String} src 图片地址
   * @param {Object} options 选项
   */
  async loadImage(src, options = {}) {
    if (this.resourceCache.has(src)) {
      return this.resourceCache.get(src);
    }

    if (this.loadingResources.has(src)) {
      return new Promise((resolve) => {
        const checkLoading = () => {
          if (this.resourceCache.has(src)) {
            resolve(this.resourceCache.get(src));
          } else {
            setTimeout(checkLoading, 100);
          }
        };
        checkLoading();
      });
    }

    this.loadingResources.add(src);

    try {
      const image = await this.libLazyLoader.loadImage(src, options);
      this.resourceCache.set(src, image);
      this.loadingResources.delete(src);
      return image;
    } catch (error) {
      this.loadingResources.delete(src);
      throw error;
    }
  }

  /**
   * 懒加载CSS
   * @param {String} href CSS文件地址
   * @param {Object} options 选项
   */
  async loadCSS(href, options = {}) {
    if (this.resourceCache.has(href)) {
      return this.resourceCache.get(href);
    }

    try {
      const link = await this.libLazyLoader.loadCSS(href, options);
      this.resourceCache.set(href, link);
      return link;
    } catch (error) {
      throw error;
    }
  }

  /**
   * 懒加载JavaScript
   * @param {String} src JS文件地址
   * @param {Object} options 选项
   */
  async loadScript(src, options = {}) {
    if (this.resourceCache.has(src)) {
      return this.resourceCache.get(src);
    }

    try {
      const script = await this.libLazyLoader.loadScript(src, options);
      this.resourceCache.set(src, script);
      return script;
    } catch (error) {
      throw error;
    }
  }
}

// 创建全局懒加载器实例
export const vueComponentLazyLoader = new VueComponentLazyLoader();
export const vueRouteLazyLoader = new VueRouteLazyLoader();
export const vueResourceLazyLoader = new VueResourceLazyLoader();

/**
 * Vue懒加载插件
 */
export const LazyLoadPlugin = {
  install(Vue, options = {}) {
    // 添加全局属性
    Vue.prototype.$lazyLoader = vueComponentLazyLoader;
    Vue.prototype.$routeLazyLoader = vueRouteLazyLoader;
    Vue.prototype.$resourceLazyLoader = vueResourceLazyLoader;
    
    // 添加全局方法
    Vue.prototype.$lazyComponent = (importFunction, options) => {
      return vueComponentLazyLoader.createLazyComponent(importFunction, options);
    };
    
    Vue.prototype.$lazyRoute = (importFunction, options) => {
      return vueRouteLazyLoader.createLazyRoute(importFunction, options);
    };
    
    Vue.prototype.$loadImage = (src, options) => {
      return vueResourceLazyLoader.loadImage(src, options);
    };
    
    // 注册全局组件
    Vue.component('LazyComponent', {
      props: {
        importFunction: {
          type: Function,
          required: true
        },
        options: {
          type: Object,
          default: () => ({})
        }
      },
      render(h) {
        const LazyComp = vueComponentLazyLoader.createLazyComponent(
          this.importFunction,
          this.options
        );
        return h(LazyComp);
      }
    });
  }
};

// 导出所有功能
export {
  LazyLoader
};
