/**
 * 增强事件总线系统
 * 基于lib库的事件总线，提供Vue项目的事件管理功能
 */

import eventBus from '../../lib/utils/event-bus.js';
import { debounce, throttle } from '../../lib/utils/PerformanceOptimizer.js';

/**
 * 增强事件总线类
 * 扩展lib库的事件总线功能
 */
export class EnhancedEventBus {
  constructor() {
    this.libEventBus = eventBus;
    this.listeners = new Map();
    this.onceListeners = new Set();
    this.namespaces = new Map();
    this.middleware = [];
    this.isEnabled = true;
    this.debugMode = false;
  }

  /**
   * 监听事件
   * @param {String} event 事件名称
   * @param {Function} handler 事件处理函数
   * @param {Object} options 选项
   */
  on(event, handler, options = {}) {
    if (!this.isEnabled) return;

    // 处理命名空间
    const { namespace, eventName } = this.parseEvent(event);
    
    // 应用中间件
    const processedHandler = this.applyMiddleware(handler, options);
    
    // 处理防抖和节流
    let finalHandler = processedHandler;
    if (options.debounce) {
      finalHandler = debounce(processedHandler, options.debounce);
    } else if (options.throttle) {
      finalHandler = throttle(processedHandler, options.throttle);
    }
    
    // 记录监听器
    this.recordListener(event, finalHandler, options);
    
    // 调用lib库的事件监听
    this.libEventBus.on(eventName, finalHandler);
    
    if (this.debugMode) {
      console.log(`[EventBus] 监听事件: ${event}`, { options, namespace });
    }
    
    return this;
  }

  /**
   * 监听一次性事件
   * @param {String} event 事件名称
   * @param {Function} handler 事件处理函数
   * @param {Object} options 选项
   */
  once(event, handler, options = {}) {
    const wrappedHandler = (...args) => {
      handler(...args);
      this.off(event, wrappedHandler);
      this.onceListeners.delete(wrappedHandler);
    };
    
    this.onceListeners.add(wrappedHandler);
    return this.on(event, wrappedHandler, options);
  }

  /**
   * 移除事件监听
   * @param {String} event 事件名称
   * @param {Function} handler 事件处理函数
   */
  off(event, handler) {
    const { eventName } = this.parseEvent(event);
    
    // 从lib库移除监听
    this.libEventBus.off(eventName, handler);
    
    // 从本地记录中移除
    this.removeListener(event, handler);
    
    if (this.debugMode) {
      console.log(`[EventBus] 移除监听: ${event}`);
    }
    
    return this;
  }

  /**
   * 触发事件
   * @param {String} event 事件名称
   * @param {*} data 事件数据
   */
  emit(event, data) {
    if (!this.isEnabled) return;

    const { namespace, eventName } = this.parseEvent(event);
    
    if (this.debugMode) {
      console.log(`[EventBus] 触发事件: ${event}`, { data, namespace });
    }
    
    // 调用lib库的事件触发
    this.libEventBus.emit(eventName, data);
    
    return this;
  }

  /**
   * 解析事件名称和命名空间
   * @param {String} event 事件字符串
   */
  parseEvent(event) {
    const parts = event.split(':');
    if (parts.length > 1) {
      return {
        namespace: parts[0],
        eventName: parts.slice(1).join(':')
      };
    }
    return {
      namespace: 'default',
      eventName: event
    };
  }

  /**
   * 记录监听器
   * @param {String} event 事件名称
   * @param {Function} handler 处理函数
   * @param {Object} options 选项
   */
  recordListener(event, handler, options) {
    if (!this.listeners.has(event)) {
      this.listeners.set(event, []);
    }
    
    this.listeners.get(event).push({
      handler,
      options,
      timestamp: Date.now()
    });
  }

  /**
   * 移除监听器记录
   * @param {String} event 事件名称
   * @param {Function} handler 处理函数
   */
  removeListener(event, handler) {
    if (this.listeners.has(event)) {
      const listeners = this.listeners.get(event);
      const index = listeners.findIndex(l => l.handler === handler);
      if (index > -1) {
        listeners.splice(index, 1);
      }
      
      if (listeners.length === 0) {
        this.listeners.delete(event);
      }
    }
  }

  /**
   * 应用中间件
   * @param {Function} handler 原始处理函数
   * @param {Object} options 选项
   */
  applyMiddleware(handler, options) {
    let processedHandler = handler;
    
    for (const middleware of this.middleware) {
      processedHandler = middleware(processedHandler, options);
    }
    
    return processedHandler;
  }

  /**
   * 添加中间件
   * @param {Function} middleware 中间件函数
   */
  use(middleware) {
    this.middleware.push(middleware);
    return this;
  }

  /**
   * 移除中间件
   * @param {Function} middleware 中间件函数
   */
  removeMiddleware(middleware) {
    const index = this.middleware.indexOf(middleware);
    if (index > -1) {
      this.middleware.splice(index, 1);
    }
    return this;
  }

  /**
   * 清空所有监听器
   * @param {String} namespace 命名空间（可选）
   */
  clear(namespace) {
    if (namespace) {
      // 清空指定命名空间的监听器
      for (const [event] of this.listeners) {
        const { namespace: eventNamespace } = this.parseEvent(event);
        if (eventNamespace === namespace) {
          this.listeners.delete(event);
        }
      }
    } else {
      // 清空所有监听器
      this.listeners.clear();
      this.onceListeners.clear();
    }
    
    // 清空lib库的监听器
    this.libEventBus.clear();
    
    return this;
  }

  /**
   * 获取监听器统计信息
   */
  getStats() {
    const stats = {
      totalListeners: 0,
      byEvent: {},
      byNamespace: {},
      onceListeners: this.onceListeners.size,
      middleware: this.middleware.length,
      isEnabled: this.isEnabled
    };
    
    for (const [event, listeners] of this.listeners) {
      const { namespace } = this.parseEvent(event);
      
      stats.totalListeners += listeners.length;
      stats.byEvent[event] = listeners.length;
      
      if (!stats.byNamespace[namespace]) {
        stats.byNamespace[namespace] = 0;
      }
      stats.byNamespace[namespace] += listeners.length;
    }
    
    return stats;
  }

  /**
   * 启用事件总线
   */
  enable() {
    this.isEnabled = true;
    return this;
  }

  /**
   * 禁用事件总线
   */
  disable() {
    this.isEnabled = false;
    return this;
  }

  /**
   * 启用调试模式
   */
  enableDebug() {
    this.debugMode = true;
    return this;
  }

  /**
   * 禁用调试模式
   */
  disableDebug() {
    this.debugMode = false;
    return this;
  }

  /**
   * 获取lib库事件总线实例
   */
  getLibEventBus() {
    return this.libEventBus;
  }
}

// 创建全局增强事件总线实例
export const enhancedEventBus = new EnhancedEventBus();

/**
 * Vue事件总线混入
 * 为Vue组件提供事件总线功能
 */
export const EventBusMixin = {
  beforeCreate() {
    this._eventBusListeners = [];
  },
  
  beforeDestroy() {
    // 自动清理组件的事件监听器
    this._eventBusListeners.forEach(({ event, handler }) => {
      enhancedEventBus.off(event, handler);
    });
    this._eventBusListeners = [];
  },
  
  methods: {
    /**
     * 监听事件（自动清理）
     */
    $busOn(event, handler, options) {
      enhancedEventBus.on(event, handler, options);
      this._eventBusListeners.push({ event, handler });
      return this;
    },
    
    /**
     * 监听一次性事件（自动清理）
     */
    $busOnce(event, handler, options) {
      enhancedEventBus.once(event, handler, options);
      return this;
    },
    
    /**
     * 移除事件监听
     */
    $busOff(event, handler) {
      enhancedEventBus.off(event, handler);
      
      // 从本地记录中移除
      const index = this._eventBusListeners.findIndex(
        l => l.event === event && l.handler === handler
      );
      if (index > -1) {
        this._eventBusListeners.splice(index, 1);
      }
      
      return this;
    },
    
    /**
     * 触发事件
     */
    $busEmit(event, data) {
      enhancedEventBus.emit(event, data);
      return this;
    }
  }
};

/**
 * Vue事件总线插件
 */
export const EventBusPlugin = {
  install(Vue, options = {}) {
    // 配置增强事件总线
    if (options.debug) {
      enhancedEventBus.enableDebug();
    }
    
    if (options.middleware) {
      options.middleware.forEach(middleware => {
        enhancedEventBus.use(middleware);
      });
    }
    
    // 注册全局混入
    if (options.enableGlobalMixin !== false) {
      Vue.mixin(EventBusMixin);
    }
    
    // 添加全局属性
    Vue.prototype.$eventBus = enhancedEventBus;
    Vue.prototype.$bus = enhancedEventBus; // 简写
    
    // 添加全局方法
    Vue.prototype.$busOn = (event, handler, options) => {
      return enhancedEventBus.on(event, handler, options);
    };
    
    Vue.prototype.$busOff = (event, handler) => {
      return enhancedEventBus.off(event, handler);
    };
    
    Vue.prototype.$busEmit = (event, data) => {
      return enhancedEventBus.emit(event, data);
    };
    
    Vue.prototype.$busOnce = (event, handler, options) => {
      return enhancedEventBus.once(event, handler, options);
    };
  }
};

// 常用中间件
export const commonMiddleware = {
  /**
   * 日志中间件
   */
  logger: (handler, options) => {
    return (...args) => {
      console.log('[EventBus] 事件处理:', args);
      return handler(...args);
    };
  },
  
  /**
   * 错误处理中间件
   */
  errorHandler: (handler, options) => {
    return (...args) => {
      try {
        return handler(...args);
      } catch (error) {
        console.error('[EventBus] 事件处理错误:', error);
        if (options.onError) {
          options.onError(error);
        }
      }
    };
  },
  
  /**
   * 性能监控中间件
   */
  performance: (handler, options) => {
    return (...args) => {
      const startTime = performance.now();
      const result = handler(...args);
      const endTime = performance.now();
      
      console.log(`[EventBus] 事件处理耗时: ${endTime - startTime}ms`);
      return result;
    };
  }
};

// 导出默认实例和类
export default enhancedEventBus;
