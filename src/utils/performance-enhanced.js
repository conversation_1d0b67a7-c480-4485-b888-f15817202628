/**
 * 性能优化增强工具
 * 集成lib库的性能优化功能，提供Vue项目的性能监控和优化
 */

import { 
  performanceMonitor, 
  debounce, 
  throttle, 
  VirtualScrollManager,
  ImageLazyLoader 
} from '../../lib/utils/PerformanceOptimizer.js';

/**
 * Vue性能监控混入
 * 为Vue组件提供性能监控功能
 */
export const PerformanceMonitorMixin = {
  data() {
    return {
      performanceMetrics: {
        renderTime: 0,
        updateTime: 0,
        mountTime: 0
      }
    };
  },
  
  created() {
    this._performanceStartTime = performance.now();
  },
  
  mounted() {
    const mountTime = performance.now() - this._performanceStartTime;
    this.performanceMetrics.mountTime = mountTime;
    
    // 记录组件挂载时间
    performanceMonitor.recordMetric(`component.${this.$options.name}.mount`, mountTime);
    
    // 启动性能监控
    if (!performanceMonitor.isRunning()) {
      performanceMonitor.start();
    }
  },
  
  updated() {
    const updateTime = performance.now() - this._performanceStartTime;
    this.performanceMetrics.updateTime = updateTime;
    
    // 记录组件更新时间
    performanceMonitor.recordMetric(`component.${this.$options.name}.update`, updateTime);
  },
  
  beforeDestroy() {
    // 记录组件销毁前的性能指标
    const totalTime = performance.now() - this._performanceStartTime;
    performanceMonitor.recordMetric(`component.${this.$options.name}.total`, totalTime);
  },
  
  methods: {
    /**
     * 获取组件性能报告
     */
    getPerformanceReport() {
      return {
        component: this.$options.name,
        metrics: this.performanceMetrics,
        globalReport: performanceMonitor.getPerformanceReport()
      };
    },
    
    /**
     * 记录自定义性能指标
     */
    recordPerformanceMetric(name, value) {
      performanceMonitor.recordMetric(`component.${this.$options.name}.${name}`, value);
    }
  }
};

/**
 * Vue防抖指令
 * 使用lib库的debounce功能
 */
export const DebounceDirective = {
  bind(el, binding) {
    const delay = binding.arg || 300;
    const debouncedHandler = debounce(binding.value, delay);
    
    el._debouncedHandler = debouncedHandler;
    el.addEventListener('input', debouncedHandler);
  },
  
  unbind(el) {
    if (el._debouncedHandler) {
      el.removeEventListener('input', el._debouncedHandler);
      delete el._debouncedHandler;
    }
  }
};

/**
 * Vue节流指令
 * 使用lib库的throttle功能
 */
export const ThrottleDirective = {
  bind(el, binding) {
    const delay = binding.arg || 100;
    const throttledHandler = throttle(binding.value, delay);
    
    el._throttledHandler = throttledHandler;
    el.addEventListener(binding.modifiers.scroll ? 'scroll' : 'click', throttledHandler);
  },
  
  unbind(el) {
    if (el._throttledHandler) {
      const event = el._throttledHandler._event || 'click';
      el.removeEventListener(event, el._throttledHandler);
      delete el._throttledHandler;
    }
  }
};

/**
 * Vue虚拟滚动组件
 * 基于lib库的VirtualScrollManager
 */
export const VirtualScrollComponent = {
  name: 'VirtualScroll',
  props: {
    items: {
      type: Array,
      required: true
    },
    itemHeight: {
      type: Number,
      default: 50
    },
    containerHeight: {
      type: Number,
      default: 400
    },
    buffer: {
      type: Number,
      default: 5
    }
  },
  
  data() {
    return {
      virtualScrollManager: null,
      visibleItems: [],
      scrollTop: 0
    };
  },
  
  mounted() {
    this.initVirtualScroll();
  },
  
  beforeDestroy() {
    if (this.virtualScrollManager) {
      this.virtualScrollManager.destroy();
    }
  },
  
  watch: {
    items: {
      handler() {
        this.updateVirtualScroll();
      },
      deep: true
    }
  },
  
  methods: {
    initVirtualScroll() {
      this.virtualScrollManager = new VirtualScrollManager({
        itemHeight: this.itemHeight,
        containerHeight: this.containerHeight,
        items: this.items,
        buffer: this.buffer,
        onUpdate: (visibleItems, scrollTop) => {
          this.visibleItems = visibleItems;
          this.scrollTop = scrollTop;
        }
      });
      
      this.virtualScrollManager.init(this.$refs.container);
    },
    
    updateVirtualScroll() {
      if (this.virtualScrollManager) {
        this.virtualScrollManager.updateItems(this.items);
      }
    },
    
    scrollToIndex(index) {
      if (this.virtualScrollManager) {
        this.virtualScrollManager.scrollToIndex(index);
      }
    }
  },
  
  render(h) {
    return h('div', {
      ref: 'container',
      class: 'virtual-scroll-container',
      style: {
        height: `${this.containerHeight}px`,
        overflow: 'auto'
      }
    }, [
      h('div', {
        class: 'virtual-scroll-content',
        style: {
          height: `${this.items.length * this.itemHeight}px`,
          position: 'relative'
        }
      }, this.visibleItems.map((item, index) => {
        return this.$scopedSlots.default({
          item: item.data,
          index: item.index,
          style: {
            position: 'absolute',
            top: `${item.top}px`,
            height: `${this.itemHeight}px`,
            width: '100%'
          }
        });
      }))
    ]);
  }
};

/**
 * Vue图片懒加载指令
 * 基于lib库的ImageLazyLoader
 */
export const LazyLoadDirective = {
  bind(el, binding) {
    const lazyLoader = new ImageLazyLoader({
      threshold: binding.arg || 0.1,
      rootMargin: '50px'
    });
    
    el._lazyLoader = lazyLoader;
    lazyLoader.observe(el);
    
    // 设置占位符
    if (binding.value && binding.value.placeholder) {
      el.src = binding.value.placeholder;
    }
    
    // 设置数据源
    if (binding.value && binding.value.src) {
      el.dataset.src = binding.value.src;
    }
  },
  
  update(el, binding) {
    if (binding.value && binding.value.src && el.dataset.src !== binding.value.src) {
      el.dataset.src = binding.value.src;
      if (el._lazyLoader) {
        el._lazyLoader.refresh(el);
      }
    }
  },
  
  unbind(el) {
    if (el._lazyLoader) {
      el._lazyLoader.unobserve(el);
      delete el._lazyLoader;
    }
  }
};

/**
 * 性能优化工具类
 */
export class PerformanceEnhancer {
  constructor() {
    this.isEnabled = true;
    this.metrics = new Map();
  }
  
  /**
   * 启用性能优化
   */
  enable() {
    this.isEnabled = true;
    performanceMonitor.start();
  }
  
  /**
   * 禁用性能优化
   */
  disable() {
    this.isEnabled = false;
    performanceMonitor.stop();
  }
  
  /**
   * 创建防抖函数
   */
  createDebounced(fn, delay = 300) {
    return debounce(fn, delay);
  }
  
  /**
   * 创建节流函数
   */
  createThrottled(fn, delay = 100) {
    return throttle(fn, delay);
  }
  
  /**
   * 测量函数执行时间
   */
  measureFunction(fn, name) {
    return (...args) => {
      const startTime = performance.now();
      const result = fn.apply(this, args);
      const endTime = performance.now();
      
      const executionTime = endTime - startTime;
      this.recordMetric(name, executionTime);
      
      return result;
    };
  }
  
  /**
   * 测量异步函数执行时间
   */
  async measureAsyncFunction(fn, name) {
    return async (...args) => {
      const startTime = performance.now();
      const result = await fn.apply(this, args);
      const endTime = performance.now();
      
      const executionTime = endTime - startTime;
      this.recordMetric(name, executionTime);
      
      return result;
    };
  }
  
  /**
   * 记录性能指标
   */
  recordMetric(name, value) {
    if (!this.isEnabled) return;
    
    performanceMonitor.recordMetric(name, value);
    
    // 本地记录
    if (!this.metrics.has(name)) {
      this.metrics.set(name, []);
    }
    this.metrics.get(name).push({
      value,
      timestamp: Date.now()
    });
  }
  
  /**
   * 获取性能报告
   */
  getPerformanceReport() {
    return {
      global: performanceMonitor.getPerformanceReport(),
      local: Object.fromEntries(this.metrics),
      isEnabled: this.isEnabled
    };
  }
  
  /**
   * 清空性能数据
   */
  clearMetrics() {
    this.metrics.clear();
    performanceMonitor.clearMetrics();
  }
  
  /**
   * 优化Vue组件性能
   */
  optimizeVueComponent(component) {
    // 添加性能监控混入
    if (!component.mixins) {
      component.mixins = [];
    }
    component.mixins.push(PerformanceMonitorMixin);
    
    // 优化计算属性
    if (component.computed) {
      Object.keys(component.computed).forEach(key => {
        const originalComputed = component.computed[key];
        if (typeof originalComputed === 'function') {
          component.computed[key] = this.measureFunction(originalComputed, `computed.${key}`);
        }
      });
    }
    
    // 优化方法
    if (component.methods) {
      Object.keys(component.methods).forEach(key => {
        const originalMethod = component.methods[key];
        if (typeof originalMethod === 'function') {
          component.methods[key] = this.measureFunction(originalMethod, `method.${key}`);
        }
      });
    }
    
    return component;
  }
}

// 创建全局性能增强器实例
export const performanceEnhancer = new PerformanceEnhancer();

/**
 * Vue性能优化插件
 */
export const PerformancePlugin = {
  install(Vue, options = {}) {
    // 注册全局混入
    if (options.enableGlobalMonitoring !== false) {
      Vue.mixin(PerformanceMonitorMixin);
    }
    
    // 注册指令
    Vue.directive('debounce', DebounceDirective);
    Vue.directive('throttle', ThrottleDirective);
    Vue.directive('lazy-load', LazyLoadDirective);
    
    // 注册组件
    Vue.component('VirtualScroll', VirtualScrollComponent);
    
    // 添加全局属性
    Vue.prototype.$performance = performanceEnhancer;
    Vue.prototype.$performanceMonitor = performanceMonitor;
    
    // 添加全局方法
    Vue.prototype.$debounce = (fn, delay) => debounce(fn, delay);
    Vue.prototype.$throttle = (fn, delay) => throttle(fn, delay);
    
    // 启动性能监控
    if (options.autoStart !== false) {
      performanceEnhancer.enable();
    }
  }
};

// 导出所有功能
export {
  performanceMonitor,
  debounce,
  throttle,
  VirtualScrollManager,
  ImageLazyLoader
};
