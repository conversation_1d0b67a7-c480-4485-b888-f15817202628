/**
 * 增强工具函数主入口文件
 * 统一导出所有基于lib库的增强工具函数
 */

// 性能优化相关
export {
  PerformanceMonitorMixin,
  DebounceDirective,
  ThrottleDirective,
  VirtualScrollComponent,
  LazyLoadDirective,
  PerformanceEnhancer,
  performanceEnhancer,
  PerformancePlugin,
  performanceMonitor,
  debounce,
  throttle,
  VirtualScrollManager,
  ImageLazyLoader
} from '../performance-enhanced.js';

// 事件总线相关
export {
  EnhancedEventBus,
  enhancedEventBus,
  EventBusMixin,
  EventBusPlugin,
  commonMiddleware
} from '../event-bus-enhanced.js';

// 懒加载相关
export {
  VueComponentLazyLoader,
  VueRouteLazyLoader,
  VueResourceLazyLoader,
  vueComponentLazyLoader,
  vueRouteLazyLoader,
  vueResourceLazyLoader,
  LazyLoadPlugin,
  LazyLoader
} from '../lazy-loader-enhanced.js';

// 资源管理相关
export {
  VueResourceManager,
  vueResourceManager,
  ResourceManagerMixin,
  ResourceManagerPlugin,
  globalResourceManager
} from '../resource-manager-enhanced.js';

/**
 * 增强工具函数集合插件
 * 一键安装所有增强工具函数
 */
export const EnhancedUtilsPlugin = {
  install(Vue, options = {}) {
    const {
      performance = {},
      eventBus = {},
      lazyLoad = {},
      resourceManager = {}
    } = options;

    // 安装性能优化插件
    if (performance.enabled !== false) {
      Vue.use(PerformancePlugin, performance);
    }

    // 安装事件总线插件
    if (eventBus.enabled !== false) {
      Vue.use(EventBusPlugin, eventBus);
    }

    // 安装懒加载插件
    if (lazyLoad.enabled !== false) {
      Vue.use(LazyLoadPlugin, lazyLoad);
    }

    // 安装资源管理插件
    if (resourceManager.enabled !== false) {
      Vue.use(ResourceManagerPlugin, resourceManager);
    }

    // 添加全局工具函数
    Vue.prototype.$utils = {
      performance: performanceEnhancer,
      eventBus: enhancedEventBus,
      lazyLoader: vueComponentLazyLoader,
      resourceManager: vueResourceManager
    };

    // 添加全局配置
    Vue.prototype.$enhancedConfig = {
      performance: performance.enabled !== false,
      eventBus: eventBus.enabled !== false,
      lazyLoad: lazyLoad.enabled !== false,
      resourceManager: resourceManager.enabled !== false
    };
  }
};

/**
 * 增强工具函数管理器
 * 提供统一的工具函数管理接口
 */
export class EnhancedUtilsManager {
  constructor() {
    this.performance = performanceEnhancer;
    this.eventBus = enhancedEventBus;
    this.lazyLoader = vueComponentLazyLoader;
    this.resourceManager = vueResourceManager;
    this.isInitialized = false;
  }

  /**
   * 初始化所有工具函数
   * @param {Object} options 配置选项
   */
  init(options = {}) {
    if (this.isInitialized) return;

    // 初始化性能监控
    if (options.performance !== false) {
      this.performance.enable();
    }

    // 初始化事件总线
    if (options.eventBus !== false) {
      this.eventBus.enable();
      if (options.debug) {
        this.eventBus.enableDebug();
      }
    }

    // 初始化资源管理
    if (options.resourceManager !== false) {
      this.resourceManager.enable();
    }

    this.isInitialized = true;
  }

  /**
   * 获取所有工具函数的状态
   */
  getStatus() {
    return {
      performance: {
        enabled: this.performance.isEnabled,
        metrics: this.performance.getPerformanceReport()
      },
      eventBus: {
        enabled: this.eventBus.isEnabled,
        stats: this.eventBus.getStats()
      },
      lazyLoader: {
        componentCache: this.lazyLoader.getCacheStats(),
        routeCache: vueRouteLazyLoader.getCacheStats()
      },
      resourceManager: {
        enabled: this.resourceManager.isEnabled,
        stats: this.resourceManager.getResourceStats()
      },
      isInitialized: this.isInitialized
    };
  }

  /**
   * 清理所有工具函数
   */
  cleanup() {
    // 清理性能监控
    this.performance.clearMetrics();

    // 清理事件总线
    this.eventBus.clear();

    // 清理懒加载缓存
    this.lazyLoader.clearCache();
    vueRouteLazyLoader.clearCache();

    // 清理资源管理
    this.resourceManager.cleanupAllResources();

    this.isInitialized = false;
  }

  /**
   * 重置所有工具函数
   */
  reset() {
    this.cleanup();
    this.init();
  }

  /**
   * 启用所有工具函数
   */
  enableAll() {
    this.performance.enable();
    this.eventBus.enable();
    this.resourceManager.enable();
  }

  /**
   * 禁用所有工具函数
   */
  disableAll() {
    this.performance.disable();
    this.eventBus.disable();
    this.resourceManager.disable();
  }
}

// 创建全局增强工具函数管理器实例
export const enhancedUtilsManager = new EnhancedUtilsManager();

/**
 * 增强工具函数工厂
 * 提供便捷的工具函数创建方法
 */
export class EnhancedUtilsFactory {
  /**
   * 创建防抖函数
   * @param {Function} fn 原函数
   * @param {Number} delay 延迟时间
   * @returns {Function} 防抖函数
   */
  static createDebounced(fn, delay = 300) {
    return debounce(fn, delay);
  }

  /**
   * 创建节流函数
   * @param {Function} fn 原函数
   * @param {Number} delay 延迟时间
   * @returns {Function} 节流函数
   */
  static createThrottled(fn, delay = 100) {
    return throttle(fn, delay);
  }

  /**
   * 创建性能监控函数
   * @param {Function} fn 原函数
   * @param {String} name 函数名称
   * @returns {Function} 监控函数
   */
  static createMonitored(fn, name) {
    return performanceEnhancer.measureFunction(fn, name);
  }

  /**
   * 创建懒加载组件
   * @param {Function} importFunction 导入函数
   * @param {Object} options 选项
   * @returns {Object} 懒加载组件
   */
  static createLazyComponent(importFunction, options = {}) {
    return vueComponentLazyLoader.createLazyComponent(importFunction, options);
  }

  /**
   * 创建虚拟滚动组件
   * @param {Object} options 选项
   * @returns {Object} 虚拟滚动组件
   */
  static createVirtualScroll(options = {}) {
    return {
      ...VirtualScrollComponent,
      props: {
        ...VirtualScrollComponent.props,
        ...options.props
      }
    };
  }

  /**
   * 创建事件总线实例
   * @param {Object} options 选项
   * @returns {EnhancedEventBus} 事件总线实例
   */
  static createEventBus(options = {}) {
    const eventBus = new EnhancedEventBus();
    
    if (options.debug) {
      eventBus.enableDebug();
    }
    
    if (options.middleware) {
      options.middleware.forEach(middleware => {
        eventBus.use(middleware);
      });
    }
    
    return eventBus;
  }

  /**
   * 创建资源管理器实例
   * @param {Object} options 选项
   * @returns {VueResourceManager} 资源管理器实例
   */
  static createResourceManager(options = {}) {
    const resourceManager = new VueResourceManager();
    
    if (options.enabled !== false) {
      resourceManager.enable();
    }
    
    return resourceManager;
  }
}

/**
 * 增强工具函数常量
 */
export const ENHANCED_UTILS_CONSTANTS = {
  // 性能相关常量
  PERFORMANCE: {
    DEFAULT_DEBOUNCE_DELAY: 300,
    DEFAULT_THROTTLE_DELAY: 100,
    DEFAULT_VIRTUAL_SCROLL_ITEM_HEIGHT: 50,
    DEFAULT_VIRTUAL_SCROLL_BUFFER: 5
  },
  
  // 事件总线相关常量
  EVENT_BUS: {
    DEFAULT_NAMESPACE: 'default',
    SYSTEM_EVENTS: {
      COMPONENT_MOUNTED: 'component:mounted',
      COMPONENT_DESTROYED: 'component:destroyed',
      ROUTE_CHANGED: 'route:changed',
      THEME_CHANGED: 'theme:changed'
    }
  },
  
  // 懒加载相关常量
  LAZY_LOAD: {
    DEFAULT_RETRY_COUNT: 3,
    DEFAULT_RETRY_DELAY: 1000,
    DEFAULT_LOADING_DELAY: 200,
    DEFAULT_TIMEOUT: 10000
  },
  
  // 资源管理相关常量
  RESOURCE_MANAGER: {
    RESOURCE_TYPES: {
      EVENT_LISTENER: 'eventListener',
      TIMER: 'timer',
      OBSERVER: 'observer',
      COMPONENT: 'component',
      GLOBAL: 'global'
    },
    CLEANUP_PRIORITIES: {
      HIGH: 100,
      NORMAL: 50,
      LOW: 10
    }
  }
};

// 默认导出
export default {
  // 插件
  EnhancedUtilsPlugin,
  PerformancePlugin,
  EventBusPlugin,
  LazyLoadPlugin,
  ResourceManagerPlugin,
  
  // 管理器
  enhancedUtilsManager,
  performanceEnhancer,
  enhancedEventBus,
  vueResourceManager,
  
  // 工厂
  EnhancedUtilsFactory,
  
  // 常量
  ENHANCED_UTILS_CONSTANTS
};
