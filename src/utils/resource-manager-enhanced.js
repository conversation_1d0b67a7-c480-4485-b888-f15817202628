/**
 * 增强资源管理器
 * 基于lib库的资源管理功能，提供Vue项目的资源管理解决方案
 */

import { globalResourceManager } from '../../lib/utils/ResourceManager.js';

/**
 * Vue资源管理器
 * 专门为Vue组件提供资源管理功能
 */
export class VueResourceManager {
  constructor() {
    this.libResourceManager = globalResourceManager;
    this.componentResources = new Map();
    this.globalResources = new Set();
    this.cleanupTasks = new Map();
    this.isEnabled = true;
  }

  /**
   * 注册组件资源
   * @param {String} componentId 组件ID
   * @param {Object} resource 资源对象
   * @param {Object} options 选项
   */
  registerComponentResource(componentId, resource, options = {}) {
    if (!this.isEnabled) return;

    if (!this.componentResources.has(componentId)) {
      this.componentResources.set(componentId, new Set());
    }

    const resourceInfo = {
      resource,
      type: options.type || 'unknown',
      timestamp: Date.now(),
      cleanup: options.cleanup,
      priority: options.priority || 0
    };

    this.componentResources.get(componentId).add(resourceInfo);
    
    // 注册到lib库资源管理器
    this.libResourceManager.register(resource, options);

    return resourceInfo;
  }

  /**
   * 注册全局资源
   * @param {Object} resource 资源对象
   * @param {Object} options 选项
   */
  registerGlobalResource(resource, options = {}) {
    if (!this.isEnabled) return;

    const resourceInfo = {
      resource,
      type: options.type || 'global',
      timestamp: Date.now(),
      cleanup: options.cleanup,
      persistent: options.persistent || false
    };

    this.globalResources.add(resourceInfo);
    
    // 注册到lib库资源管理器
    this.libResourceManager.register(resource, options);

    return resourceInfo;
  }

  /**
   * 清理组件资源
   * @param {String} componentId 组件ID
   */
  cleanupComponentResources(componentId) {
    if (!this.componentResources.has(componentId)) return;

    const resources = this.componentResources.get(componentId);
    
    for (const resourceInfo of resources) {
      try {
        // 执行自定义清理函数
        if (resourceInfo.cleanup && typeof resourceInfo.cleanup === 'function') {
          resourceInfo.cleanup(resourceInfo.resource);
        }
        
        // 从lib库资源管理器中移除
        this.libResourceManager.unregister(resourceInfo.resource);
      } catch (error) {
        console.error(`清理组件资源失败 (${componentId}):`, error);
      }
    }

    this.componentResources.delete(componentId);
  }

  /**
   * 清理全局资源
   * @param {Boolean} includePersistent 是否包含持久资源
   */
  cleanupGlobalResources(includePersistent = false) {
    for (const resourceInfo of this.globalResources) {
      if (!includePersistent && resourceInfo.persistent) {
        continue;
      }

      try {
        // 执行自定义清理函数
        if (resourceInfo.cleanup && typeof resourceInfo.cleanup === 'function') {
          resourceInfo.cleanup(resourceInfo.resource);
        }
        
        // 从lib库资源管理器中移除
        this.libResourceManager.unregister(resourceInfo.resource);
        
        this.globalResources.delete(resourceInfo);
      } catch (error) {
        console.error('清理全局资源失败:', error);
      }
    }
  }

  /**
   * 清理所有资源
   */
  cleanupAllResources() {
    // 清理所有组件资源
    for (const componentId of this.componentResources.keys()) {
      this.cleanupComponentResources(componentId);
    }

    // 清理所有全局资源
    this.cleanupGlobalResources(true);

    // 清理lib库资源管理器
    this.libResourceManager.cleanup();
  }

  /**
   * 添加清理任务
   * @param {String} taskId 任务ID
   * @param {Function} task 清理任务
   * @param {Object} options 选项
   */
  addCleanupTask(taskId, task, options = {}) {
    this.cleanupTasks.set(taskId, {
      task,
      priority: options.priority || 0,
      delay: options.delay || 0,
      condition: options.condition
    });
  }

  /**
   * 移除清理任务
   * @param {String} taskId 任务ID
   */
  removeCleanupTask(taskId) {
    this.cleanupTasks.delete(taskId);
  }

  /**
   * 执行清理任务
   * @param {String} taskId 任务ID（可选）
   */
  async executeCleanupTasks(taskId) {
    if (taskId) {
      // 执行指定任务
      const taskInfo = this.cleanupTasks.get(taskId);
      if (taskInfo) {
        await this.executeTask(taskInfo);
      }
    } else {
      // 执行所有任务
      const sortedTasks = Array.from(this.cleanupTasks.entries())
        .sort(([, a], [, b]) => b.priority - a.priority);

      for (const [id, taskInfo] of sortedTasks) {
        try {
          await this.executeTask(taskInfo);
        } catch (error) {
          console.error(`清理任务执行失败 (${id}):`, error);
        }
      }
    }
  }

  /**
   * 执行单个清理任务
   * @param {Object} taskInfo 任务信息
   */
  async executeTask(taskInfo) {
    // 检查执行条件
    if (taskInfo.condition && !taskInfo.condition()) {
      return;
    }

    // 延迟执行
    if (taskInfo.delay > 0) {
      await new Promise(resolve => setTimeout(resolve, taskInfo.delay));
    }

    // 执行任务
    await taskInfo.task();
  }

  /**
   * 获取资源统计信息
   */
  getResourceStats() {
    const stats = {
      components: {},
      global: {
        total: this.globalResources.size,
        persistent: 0,
        temporary: 0
      },
      cleanupTasks: this.cleanupTasks.size,
      libStats: this.libResourceManager.getStats()
    };

    // 统计组件资源
    for (const [componentId, resources] of this.componentResources) {
      stats.components[componentId] = {
        total: resources.size,
        byType: {}
      };

      for (const resourceInfo of resources) {
        const type = resourceInfo.type;
        if (!stats.components[componentId].byType[type]) {
          stats.components[componentId].byType[type] = 0;
        }
        stats.components[componentId].byType[type]++;
      }
    }

    // 统计全局资源
    for (const resourceInfo of this.globalResources) {
      if (resourceInfo.persistent) {
        stats.global.persistent++;
      } else {
        stats.global.temporary++;
      }
    }

    return stats;
  }

  /**
   * 启用资源管理
   */
  enable() {
    this.isEnabled = true;
  }

  /**
   * 禁用资源管理
   */
  disable() {
    this.isEnabled = false;
  }

  /**
   * 获取lib库资源管理器实例
   */
  getLibResourceManager() {
    return this.libResourceManager;
  }
}

// 创建全局Vue资源管理器实例
export const vueResourceManager = new VueResourceManager();

/**
 * Vue资源管理混入
 * 为Vue组件提供自动资源管理功能
 */
export const ResourceManagerMixin = {
  beforeCreate() {
    this._componentId = `${this.$options.name || 'Anonymous'}-${this._uid}`;
    this._resources = new Set();
  },

  beforeDestroy() {
    // 自动清理组件资源
    vueResourceManager.cleanupComponentResources(this._componentId);
  },

  methods: {
    /**
     * 注册资源
     * @param {Object} resource 资源对象
     * @param {Object} options 选项
     */
    $registerResource(resource, options = {}) {
      const resourceInfo = vueResourceManager.registerComponentResource(
        this._componentId,
        resource,
        options
      );
      this._resources.add(resourceInfo);
      return resourceInfo;
    },

    /**
     * 注册事件监听器资源
     * @param {Object} target 目标对象
     * @param {String} event 事件名称
     * @param {Function} handler 处理函数
     * @param {Object} options 选项
     */
    $registerEventListener(target, event, handler, options = {}) {
      target.addEventListener(event, handler, options);
      
      return this.$registerResource(
        { target, event, handler },
        {
          type: 'eventListener',
          cleanup: (resource) => {
            resource.target.removeEventListener(resource.event, resource.handler);
          }
        }
      );
    },

    /**
     * 注册定时器资源
     * @param {Function} callback 回调函数
     * @param {Number} delay 延迟时间
     * @param {Boolean} isInterval 是否为间隔定时器
     */
    $registerTimer(callback, delay, isInterval = false) {
      const timerId = isInterval 
        ? setInterval(callback, delay)
        : setTimeout(callback, delay);
      
      return this.$registerResource(
        { timerId, isInterval },
        {
          type: 'timer',
          cleanup: (resource) => {
            if (resource.isInterval) {
              clearInterval(resource.timerId);
            } else {
              clearTimeout(resource.timerId);
            }
          }
        }
      );
    },

    /**
     * 注册观察者资源
     * @param {Object} observer 观察者对象
     * @param {Object} target 观察目标
     */
    $registerObserver(observer, target) {
      if (target) {
        observer.observe(target);
      }
      
      return this.$registerResource(
        { observer, target },
        {
          type: 'observer',
          cleanup: (resource) => {
            if (resource.target) {
              resource.observer.unobserve(resource.target);
            } else {
              resource.observer.disconnect();
            }
          }
        }
      );
    },

    /**
     * 获取组件资源统计
     */
    $getResourceStats() {
      return {
        componentId: this._componentId,
        resourceCount: this._resources.size,
        globalStats: vueResourceManager.getResourceStats()
      };
    }
  }
};

/**
 * Vue资源管理插件
 */
export const ResourceManagerPlugin = {
  install(Vue, options = {}) {
    // 注册全局混入
    if (options.enableGlobalMixin !== false) {
      Vue.mixin(ResourceManagerMixin);
    }

    // 添加全局属性
    Vue.prototype.$resourceManager = vueResourceManager;

    // 添加全局方法
    Vue.prototype.$registerGlobalResource = (resource, options) => {
      return vueResourceManager.registerGlobalResource(resource, options);
    };

    Vue.prototype.$cleanupGlobalResources = (includePersistent) => {
      return vueResourceManager.cleanupGlobalResources(includePersistent);
    };

    // 应用销毁时清理所有资源
    if (options.autoCleanup !== false) {
      window.addEventListener('beforeunload', () => {
        vueResourceManager.cleanupAllResources();
      });
    }

    // 启用资源管理
    vueResourceManager.enable();
  }
};

// 导出所有功能
export {
  globalResourceManager
};
