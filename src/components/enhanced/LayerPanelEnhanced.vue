<template>
  <div class="layer-panel-enhanced" ref="layerPanelContainer">
    <!-- lib库的LayerPanel组件将在这里渲染 -->
  </div>
</template>

<script>
import { componentManager } from '../../../lib/components/index.js';

export default {
  name: 'LayerPanelEnhanced',
  props: {
    // 图层系统实例
    layerSystem: {
      type: Object,
      required: true
    },
    
    // 显示选项
    showVisibilityToggle: {
      type: Boolean,
      default: true
    },
    showOpacityControl: {
      type: Boolean,
      default: true
    },
    showStyleConfig: {
      type: Boolean,
      default: true
    },
    showLayerOrder: {
      type: Boolean,
      default: true
    },
    showLayerStats: {
      type: Boolean,
      default: false
    },
    
    // 交互选项
    collapsible: {
      type: Boolean,
      default: true
    },
    defaultCollapsed: {
      type: Boolean,
      default: false
    },
    sortable: {
      type: Boolean,
      default: true
    },
    
    // 样式选项
    size: {
      type: String,
      default: 'normal',
      validator: value => ['small', 'normal', 'large'].includes(value)
    },
    theme: {
      type: String,
      default: 'light',
      validator: value => ['light', 'dark'].includes(value)
    },
    maxHeight: {
      type: [String, Number],
      default: '400px'
    }
  },
  
  data() {
    return {
      layerPanel: null,
      layers: [],
      selectedLayerId: null
    };
  },
  
  computed: {
    // 计算配置选项
    layerPanelOptions() {
      return {
        layerSystem: this.layerSystem,
        showVisibilityToggle: this.showVisibilityToggle,
        showOpacityControl: this.showOpacityControl,
        showStyleConfig: this.showStyleConfig,
        showLayerOrder: this.showLayerOrder,
        showLayerStats: this.showLayerStats,
        collapsible: this.collapsible,
        defaultCollapsed: this.defaultCollapsed,
        sortable: this.sortable,
        size: this.size,
        theme: this.theme,
        maxHeight: this.maxHeight,
        onLayerChange: this.handleLayerChange,
        onLayerSelect: this.handleLayerSelect,
        onLayerReorder: this.handleLayerReorder
      };
    }
  },
  
  watch: {
    // 监听属性变化并更新组件
    layerPanelOptions: {
      handler(newOptions) {
        if (this.layerPanel) {
          this.updateLayerPanel(newOptions);
        }
      },
      deep: true
    },
    
    // 监听图层系统变化
    layerSystem: {
      handler(newLayerSystem) {
        if (this.layerPanel && newLayerSystem) {
          this.layerPanel.setLayerSystem(newLayerSystem);
          this.refreshLayers();
        }
      },
      immediate: true
    }
  },
  
  mounted() {
    this.initLayerPanel();
  },
  
  beforeDestroy() {
    this.destroyLayerPanel();
  },
  
  methods: {
    /**
     * 初始化图层面板组件
     */
    initLayerPanel() {
      try {
        if (!this.layerSystem) {
          throw new Error('layerSystem is required');
        }
        
        // 创建lib库的LayerPanel组件
        this.layerPanel = componentManager.create(
          'layerPanel',
          this.$refs.layerPanelContainer,
          this.layerPanelOptions
        );
        
        // 注册组件到管理器
        const componentId = `layer-panel-${this._uid}`;
        componentManager.register(componentId, this.layerPanel);
        
        // 初始化图层列表
        this.refreshLayers();
        
        this.$emit('ready', this.layerPanel);
      } catch (error) {
        console.error('LayerPanel初始化失败:', error);
        this.$emit('error', error);
      }
    },
    
    /**
     * 更新图层面板配置
     */
    updateLayerPanel(options) {
      try {
        if (this.layerPanel && this.layerPanel.updateOptions) {
          this.layerPanel.updateOptions(options);
        }
      } catch (error) {
        console.error('LayerPanel更新失败:', error);
        this.$emit('error', error);
      }
    },
    
    /**
     * 销毁图层面板组件
     */
    destroyLayerPanel() {
      if (this.layerPanel) {
        try {
          const componentId = `layer-panel-${this._uid}`;
          componentManager.remove(componentId);
          this.layerPanel = null;
        } catch (error) {
          console.error('LayerPanel销毁失败:', error);
        }
      }
    },
    
    /**
     * 刷新图层列表
     */
    refreshLayers() {
      if (this.layerSystem) {
        try {
          this.layers = this.layerSystem.getAllLayers();
          if (this.layerPanel) {
            this.layerPanel.refreshLayers();
          }
        } catch (error) {
          console.error('刷新图层列表失败:', error);
        }
      }
    },
    
    /**
     * 处理图层变化事件
     */
    handleLayerChange(action, layerId, value) {
      this.$emit('layer-change', {
        action,
        layerId,
        value,
        layer: this.layerSystem ? this.layerSystem.getLayer(layerId) : null
      });
      
      // 根据操作类型触发具体事件
      switch (action) {
        case 'visibility':
          this.$emit('layer-visibility-change', layerId, value);
          break;
        case 'opacity':
          this.$emit('layer-opacity-change', layerId, value);
          break;
        case 'style':
          this.$emit('layer-style-change', layerId, value);
          break;
        case 'remove':
          this.$emit('layer-remove', layerId);
          break;
        default:
          break;
      }
    },
    
    /**
     * 处理图层选择事件
     */
    handleLayerSelect(layerId) {
      this.selectedLayerId = layerId;
      this.$emit('layer-select', layerId);
    },
    
    /**
     * 处理图层重排序事件
     */
    handleLayerReorder(layerId, targetLayerId, position) {
      this.$emit('layer-reorder', {
        layerId,
        targetLayerId,
        position
      });
    },
    
    // ==================== 公共方法 ====================
    
    /**
     * 添加图层到面板
     */
    addLayer(layer) {
      if (this.layerPanel) {
        this.layerPanel.addLayer(layer);
        this.refreshLayers();
      }
    },
    
    /**
     * 从面板移除图层
     */
    removeLayer(layerId) {
      if (this.layerPanel) {
        this.layerPanel.removeLayer(layerId);
        this.refreshLayers();
      }
    },
    
    /**
     * 选择图层
     */
    selectLayer(layerId) {
      if (this.layerPanel) {
        this.layerPanel.selectLayer(layerId);
        this.selectedLayerId = layerId;
      }
    },
    
    /**
     * 取消选择图层
     */
    deselectLayer() {
      if (this.layerPanel) {
        this.layerPanel.deselectLayer();
        this.selectedLayerId = null;
      }
    },
    
    /**
     * 展开/折叠图层
     */
    toggleLayer(layerId) {
      if (this.layerPanel) {
        this.layerPanel.toggleLayer(layerId);
      }
    },
    
    /**
     * 展开所有图层
     */
    expandAll() {
      if (this.layerPanel) {
        this.layerPanel.expandAll();
      }
    },
    
    /**
     * 折叠所有图层
     */
    collapseAll() {
      if (this.layerPanel) {
        this.layerPanel.collapseAll();
      }
    },
    
    /**
     * 设置图层可见性
     */
    setLayerVisibility(layerId, visible) {
      if (this.layerSystem) {
        this.layerSystem.setLayerVisibility(layerId, visible);
        if (this.layerPanel) {
          this.layerPanel.updateLayerVisibility(layerId, visible);
        }
      }
    },
    
    /**
     * 设置图层透明度
     */
    setLayerOpacity(layerId, opacity) {
      if (this.layerSystem) {
        this.layerSystem.setLayerOpacity(layerId, opacity);
        if (this.layerPanel) {
          this.layerPanel.updateLayerOpacity(layerId, opacity);
        }
      }
    },
    
    /**
     * 获取选中的图层ID
     */
    getSelectedLayerId() {
      return this.selectedLayerId;
    },
    
    /**
     * 获取选中的图层
     */
    getSelectedLayer() {
      if (this.selectedLayerId && this.layerSystem) {
        return this.layerSystem.getLayer(this.selectedLayerId);
      }
      return null;
    },
    
    /**
     * 获取所有图层
     */
    getLayers() {
      return this.layers;
    },
    
    /**
     * 获取当前状态
     */
    getState() {
      return {
        layers: this.layers,
        selectedLayerId: this.selectedLayerId,
        layerCount: this.layers.length
      };
    },
    
    /**
     * 获取lib库组件实例
     */
    getLayerPanelInstance() {
      return this.layerPanel;
    }
  }
};
</script>

<style scoped>
.layer-panel-enhanced {
  width: 100%;
  background: #fff;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  overflow: hidden;
}

/* 确保组件容器有足够的空间 */
.layer-panel-enhanced >>> .layer-panel-container {
  width: 100%;
  height: 100%;
}

/* 小尺寸样式 */
.layer-panel-enhanced.small {
  font-size: 12px;
}

/* 普通尺寸样式 */
.layer-panel-enhanced.normal {
  font-size: 14px;
}

/* 大尺寸样式 */
.layer-panel-enhanced.large {
  font-size: 16px;
}

/* 深色主题样式 */
.layer-panel-enhanced.dark {
  background: #1f1f1f;
  border-color: #434343;
  color: #fff;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .layer-panel-enhanced {
    font-size: 12px;
  }
}
</style>
