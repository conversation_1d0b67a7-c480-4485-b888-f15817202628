/**
 * 增强组件主入口文件
 * 统一导出所有基于lib库的Vue增强组件
 */

import TimeSliderEnhanced from './TimeSliderEnhanced.vue';
import ColorPickerEnhanced from './ColorPickerEnhanced.vue';
import LayerPanelEnhanced from './LayerPanelEnhanced.vue';
import ToolbarEnhanced from './ToolbarEnhanced.vue';

// 导出所有增强组件
export {
  TimeSliderEnhanced,
  ColorPickerEnhanced,
  LayerPanelEnhanced,
  ToolbarEnhanced
};

// 默认导出
export default {
  TimeSliderEnhanced,
  ColorPickerEnhanced,
  LayerPanelEnhanced,
  ToolbarEnhanced
};

/**
 * 增强组件安装插件
 * 用于在Vue应用中全局注册所有增强组件
 */
export const EnhancedComponentsPlugin = {
  install(Vue, options = {}) {
    // 全局注册组件
    Vue.component('TimeSliderEnhanced', TimeSliderEnhanced);
    Vue.component('ColorPickerEnhanced', ColorPickerEnhanced);
    Vue.component('LayerPanelEnhanced', LayerPanelEnhanced);
    Vue.component('ToolbarEnhanced', ToolbarEnhanced);
    
    // 设置全局配置
    if (options.theme) {
      Vue.prototype.$enhancedComponentsTheme = options.theme;
    }
    
    if (options.size) {
      Vue.prototype.$enhancedComponentsSize = options.size;
    }
    
    // 添加全局方法
    Vue.prototype.$setEnhancedComponentsTheme = function(theme) {
      this.$enhancedComponentsTheme = theme;
      // 通知所有增强组件更新主题
      this.$root.$emit('enhanced-components:theme-change', theme);
    };
    
    Vue.prototype.$setEnhancedComponentsSize = function(size) {
      this.$enhancedComponentsSize = size;
      // 通知所有增强组件更新尺寸
      this.$root.$emit('enhanced-components:size-change', size);
    };
  }
};

/**
 * 增强组件工厂
 * 提供统一的组件创建接口
 */
export class EnhancedComponentFactory {
  /**
   * 创建时间滑块组件
   * @param {Object} props 组件属性
   * @returns {Object} 组件配置
   */
  static createTimeSlider(props = {}) {
    return {
      component: TimeSliderEnhanced,
      props: {
        size: 'normal',
        theme: 'light',
        showPlayControls: true,
        showTimeDisplay: true,
        ...props
      }
    };
  }
  
  /**
   * 创建颜色选择器组件
   * @param {Object} props 组件属性
   * @returns {Object} 组件配置
   */
  static createColorPicker(props = {}) {
    return {
      component: ColorPickerEnhanced,
      props: {
        size: 'normal',
        theme: 'light',
        showPresets: true,
        showInput: true,
        ...props
      }
    };
  }
  
  /**
   * 创建图层面板组件
   * @param {Object} props 组件属性
   * @returns {Object} 组件配置
   */
  static createLayerPanel(props = {}) {
    return {
      component: LayerPanelEnhanced,
      props: {
        size: 'normal',
        theme: 'light',
        showVisibilityToggle: true,
        showOpacityControl: true,
        collapsible: true,
        ...props
      }
    };
  }
  
  /**
   * 创建工具栏组件
   * @param {Object} props 组件属性
   * @returns {Object} 组件配置
   */
  static createToolbar(props = {}) {
    return {
      component: ToolbarEnhanced,
      props: {
        size: 'normal',
        theme: 'light',
        orientation: 'horizontal',
        position: 'top-left',
        showTooltips: true,
        enableKeyboard: true,
        ...props
      }
    };
  }
  
  /**
   * 批量创建组件
   * @param {Array} configs 组件配置数组
   * @returns {Array} 组件配置数组
   */
  static createBatch(configs) {
    return configs.map(config => {
      const { type, ...props } = config;
      
      switch (type) {
        case 'timeSlider':
          return this.createTimeSlider(props);
        case 'colorPicker':
          return this.createColorPicker(props);
        case 'layerPanel':
          return this.createLayerPanel(props);
        case 'toolbar':
          return this.createToolbar(props);
        default:
          throw new Error(`Unknown enhanced component type: ${type}`);
      }
    });
  }
}

/**
 * 增强组件管理器
 * 提供增强组件的统一管理功能
 */
export class EnhancedComponentManager {
  constructor() {
    this.components = new Map();
    this.globalTheme = 'light';
    this.globalSize = 'normal';
  }
  
  /**
   * 注册组件
   * @param {String} id 组件ID
   * @param {Object} component 组件实例
   */
  register(id, component) {
    this.components.set(id, component);
  }
  
  /**
   * 获取组件
   * @param {String} id 组件ID
   * @returns {Object} 组件实例
   */
  get(id) {
    return this.components.get(id);
  }
  
  /**
   * 移除组件
   * @param {String} id 组件ID
   */
  remove(id) {
    const component = this.components.get(id);
    if (component && component.$destroy) {
      component.$destroy();
    }
    this.components.delete(id);
  }
  
  /**
   * 获取所有组件
   * @returns {Map} 组件映射
   */
  getAll() {
    return this.components;
  }
  
  /**
   * 清空所有组件
   */
  clear() {
    for (const [id, component] of this.components) {
      if (component && component.$destroy) {
        component.$destroy();
      }
    }
    this.components.clear();
  }
  
  /**
   * 设置全局主题
   * @param {String} theme 主题名称
   */
  setGlobalTheme(theme) {
    this.globalTheme = theme;
    
    // 更新所有组件的主题
    for (const component of this.components.values()) {
      if (component && component.theme !== undefined) {
        component.theme = theme;
      }
    }
  }
  
  /**
   * 设置全局尺寸
   * @param {String} size 尺寸名称
   */
  setGlobalSize(size) {
    this.globalSize = size;
    
    // 更新所有组件的尺寸
    for (const component of this.components.values()) {
      if (component && component.size !== undefined) {
        component.size = size;
      }
    }
  }
  
  /**
   * 获取组件统计信息
   * @returns {Object} 统计信息
   */
  getStats() {
    const stats = {
      total: this.components.size,
      byType: {},
      globalTheme: this.globalTheme,
      globalSize: this.globalSize
    };
    
    for (const component of this.components.values()) {
      const type = component.$options.name || 'Unknown';
      stats.byType[type] = (stats.byType[type] || 0) + 1;
    }
    
    return stats;
  }
}

// 创建全局增强组件管理器实例
export const enhancedComponentManager = new EnhancedComponentManager();

/**
 * 增强组件工具函数
 */
export const EnhancedComponentUtils = {
  /**
   * 检查是否为增强组件实例
   * @param {Object} component 组件实例
   * @returns {Boolean} 是否为增强组件
   */
  isEnhancedComponent(component) {
    return component && 
           component.$options && 
           component.$options.name && 
           component.$options.name.endsWith('Enhanced');
  },
  
  /**
   * 获取组件的lib库实例
   * @param {Object} component 增强组件实例
   * @returns {Object} lib库实例
   */
  getLibInstance(component) {
    if (!this.isEnhancedComponent(component)) {
      return null;
    }
    
    // 尝试获取各种lib库实例
    const getters = [
      'getTimeSliderInstance',
      'getColorPickerInstance',
      'getLayerPanelInstance',
      'getToolbarInstance'
    ];
    
    for (const getter of getters) {
      if (typeof component[getter] === 'function') {
        return component[getter]();
      }
    }
    
    return null;
  },
  
  /**
   * 批量设置组件主题
   * @param {Array} components 组件数组
   * @param {String} theme 主题名称
   */
  setThemeBatch(components, theme) {
    components.forEach(component => {
      if (this.isEnhancedComponent(component) && component.theme !== undefined) {
        component.theme = theme;
      }
    });
  },
  
  /**
   * 批量设置组件尺寸
   * @param {Array} components 组件数组
   * @param {String} size 尺寸名称
   */
  setSizeBatch(components, size) {
    components.forEach(component => {
      if (this.isEnhancedComponent(component) && component.size !== undefined) {
        component.size = size;
      }
    });
  }
};
