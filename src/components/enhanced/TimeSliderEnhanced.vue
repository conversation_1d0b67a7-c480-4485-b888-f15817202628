<template>
  <div class="time-slider-enhanced" ref="timeSliderContainer">
    <!-- lib库的TimeSlider组件将在这里渲染 -->
  </div>
</template>

<script>
import { componentManager } from '../../../lib/components/index.js';

export default {
  name: 'TimeSliderEnhanced',
  props: {
    // 时间范围
    startTime: {
      type: [Date, String, Number],
      default: () => new Date(Date.now() - 7 * 24 * 60 * 60 * 1000) // 7天前
    },
    endTime: {
      type: [Date, String, Number],
      default: () => new Date() // 现在
    },
    currentTime: {
      type: [Date, String, Number],
      default: null
    },
    
    // 控制选项
    step: {
      type: Number,
      default: 60 * 60 * 1000 // 1小时步长
    },
    autoPlay: {
      type: Boolean,
      default: false
    },
    playSpeed: {
      type: Number,
      default: 1000 // 播放速度（毫秒）
    },
    
    // 显示选项
    showPlayControls: {
      type: <PERSON>olean,
      default: true
    },
    showTimeDisplay: {
      type: Boolean,
      default: true
    },
    showSpeedControl: {
      type: Boolean,
      default: true
    },
    timeFormat: {
      type: String,
      default: 'YYYY-MM-DD HH:mm'
    },
    
    // 样式选项
    size: {
      type: String,
      default: 'normal',
      validator: value => ['small', 'normal', 'large'].includes(value)
    },
    theme: {
      type: String,
      default: 'light',
      validator: value => ['light', 'dark'].includes(value)
    },
    disabled: {
      type: Boolean,
      default: false
    }
  },
  
  data() {
    return {
      timeSlider: null,
      isPlaying: false,
      currentSpeed: 1000,
      internalCurrentTime: null
    };
  },
  
  computed: {
    // 计算时间范围
    timeRange() {
      return {
        start: this.normalizeTime(this.startTime),
        end: this.normalizeTime(this.endTime),
        current: this.normalizeTime(this.currentTime || this.internalCurrentTime)
      };
    },
    
    // 计算配置选项
    timeSliderOptions() {
      return {
        startTime: this.timeRange.start,
        endTime: this.timeRange.end,
        currentTime: this.timeRange.current,
        step: this.step,
        autoPlay: this.autoPlay,
        playSpeed: this.playSpeed,
        showPlayControls: this.showPlayControls,
        showTimeDisplay: this.showTimeDisplay,
        showSpeedControl: this.showSpeedControl,
        timeFormat: this.timeFormat,
        size: this.size,
        theme: this.theme,
        disabled: this.disabled,
        onChange: this.handleTimeChange,
        onPlay: this.handlePlay,
        onPause: this.handlePause,
        onSpeedChange: this.handleSpeedChange
      };
    }
  },
  
  watch: {
    // 监听属性变化并更新组件
    timeSliderOptions: {
      handler(newOptions) {
        if (this.timeSlider) {
          this.updateTimeSlider(newOptions);
        }
      },
      deep: true
    },
    
    // 监听外部时间变化
    currentTime(newTime) {
      if (this.timeSlider && newTime !== this.internalCurrentTime) {
        this.timeSlider.setCurrentTime(this.normalizeTime(newTime));
      }
    }
  },
  
  mounted() {
    this.initTimeSlider();
  },
  
  beforeDestroy() {
    this.destroyTimeSlider();
  },
  
  methods: {
    /**
     * 初始化时间滑块组件
     */
    initTimeSlider() {
      try {
        // 创建lib库的TimeSlider组件
        this.timeSlider = componentManager.create(
          'timeSlider',
          this.$refs.timeSliderContainer,
          this.timeSliderOptions
        );
        
        // 注册组件到管理器
        const componentId = `time-slider-${this._uid}`;
        componentManager.register(componentId, this.timeSlider);
        
        // 设置初始时间
        if (this.timeRange.current) {
          this.timeSlider.setCurrentTime(this.timeRange.current);
          this.internalCurrentTime = this.timeRange.current;
        }
        
        this.$emit('ready', this.timeSlider);
      } catch (error) {
        console.error('TimeSlider初始化失败:', error);
        this.$emit('error', error);
      }
    },
    
    /**
     * 更新时间滑块配置
     */
    updateTimeSlider(options) {
      try {
        if (this.timeSlider && this.timeSlider.updateOptions) {
          this.timeSlider.updateOptions(options);
        }
      } catch (error) {
        console.error('TimeSlider更新失败:', error);
        this.$emit('error', error);
      }
    },
    
    /**
     * 销毁时间滑块组件
     */
    destroyTimeSlider() {
      if (this.timeSlider) {
        try {
          const componentId = `time-slider-${this._uid}`;
          componentManager.remove(componentId);
          this.timeSlider = null;
        } catch (error) {
          console.error('TimeSlider销毁失败:', error);
        }
      }
    },
    
    /**
     * 标准化时间格式
     */
    normalizeTime(time) {
      if (!time) return null;
      
      if (time instanceof Date) {
        return time;
      }
      
      if (typeof time === 'string' || typeof time === 'number') {
        return new Date(time);
      }
      
      return null;
    },
    
    /**
     * 处理时间变化事件
     */
    handleTimeChange(time) {
      this.internalCurrentTime = time;
      this.$emit('change', time);
      this.$emit('update:currentTime', time);
    },
    
    /**
     * 处理播放事件
     */
    handlePlay(time) {
      this.isPlaying = true;
      this.$emit('play', time);
    },
    
    /**
     * 处理暂停事件
     */
    handlePause(time) {
      this.isPlaying = false;
      this.$emit('pause', time);
    },
    
    /**
     * 处理速度变化事件
     */
    handleSpeedChange(speed) {
      this.currentSpeed = speed;
      this.$emit('speed-change', speed);
    },
    
    // ==================== 公共方法 ====================
    
    /**
     * 设置当前时间
     */
    setCurrentTime(time) {
      const normalizedTime = this.normalizeTime(time);
      if (this.timeSlider && normalizedTime) {
        this.timeSlider.setCurrentTime(normalizedTime);
        this.internalCurrentTime = normalizedTime;
      }
    },
    
    /**
     * 设置时间范围
     */
    setTimeRange(startTime, endTime) {
      const start = this.normalizeTime(startTime);
      const end = this.normalizeTime(endTime);
      
      if (this.timeSlider && start && end) {
        this.timeSlider.setTimeRange(start, end);
      }
    },
    
    /**
     * 播放
     */
    play() {
      if (this.timeSlider) {
        this.timeSlider.play();
      }
    },
    
    /**
     * 暂停
     */
    pause() {
      if (this.timeSlider) {
        this.timeSlider.pause();
      }
    },
    
    /**
     * 向前一步
     */
    stepForward() {
      if (this.timeSlider) {
        this.timeSlider.stepForward();
      }
    },
    
    /**
     * 向后一步
     */
    stepBackward() {
      if (this.timeSlider) {
        this.timeSlider.stepBackward();
      }
    },
    
    /**
     * 重置到开始时间
     */
    reset() {
      if (this.timeSlider) {
        this.timeSlider.reset();
      }
    },
    
    /**
     * 设置播放速度
     */
    setPlaySpeed(speed) {
      if (this.timeSlider) {
        this.timeSlider.setPlaySpeed(speed);
        this.currentSpeed = speed;
      }
    },
    
    /**
     * 获取当前状态
     */
    getState() {
      if (this.timeSlider) {
        return {
          currentTime: this.internalCurrentTime,
          isPlaying: this.isPlaying,
          playSpeed: this.currentSpeed,
          timeRange: this.timeRange
        };
      }
      return null;
    },
    
    /**
     * 获取lib库组件实例
     */
    getTimeSliderInstance() {
      return this.timeSlider;
    }
  }
};
</script>

<style scoped>
.time-slider-enhanced {
  width: 100%;
  min-height: 60px;
  position: relative;
}

/* 确保组件容器有足够的空间 */
.time-slider-enhanced >>> .time-slider-container {
  width: 100%;
  height: 100%;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .time-slider-enhanced {
    min-height: 80px;
  }
}
</style>
