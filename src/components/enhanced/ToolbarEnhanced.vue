<template>
  <div class="toolbar-enhanced" ref="toolbarContainer">
    <!-- lib库的Toolbar组件将在这里渲染 -->
  </div>
</template>

<script>
import { componentManager } from '../../../lib/components/index.js';

export default {
  name: 'ToolbarEnhanced',
  props: {
    // 工具配置
    tools: {
      type: Array,
      default: () => [
        { id: 'marker', icon: '📍', title: '标记点', type: 'drawing' },
        { id: 'polygon', icon: '⬟', title: '多边形', type: 'drawing' },
        { id: 'polyline', icon: '📏', title: '折线', type: 'drawing' },
        { id: 'circle', icon: '⭕', title: '圆形', type: 'drawing' },
        { id: 'rectangle', icon: '⬜', title: '矩形', type: 'drawing' },
        { id: 'separator', type: 'separator' },
        { id: 'clear', icon: '🗑️', title: '清空', type: 'action' },
        { id: 'undo', icon: '↶', title: '撤销', type: 'action' },
        { id: 'redo', icon: '↷', title: '重做', type: 'action' }
      ]
    },
    
    // 布局选项
    orientation: {
      type: String,
      default: 'horizontal',
      validator: value => ['horizontal', 'vertical'].includes(value)
    },
    position: {
      type: String,
      default: 'top-left',
      validator: value => [
        'top-left', 'top-right', 'bottom-left', 'bottom-right',
        'top-center', 'bottom-center', 'left-center', 'right-center'
      ].includes(value)
    },
    
    // 样式选项
    size: {
      type: String,
      default: 'normal',
      validator: value => ['small', 'normal', 'large'].includes(value)
    },
    theme: {
      type: String,
      default: 'light',
      validator: value => ['light', 'dark'].includes(value)
    },
    
    // 交互选项
    collapsible: {
      type: Boolean,
      default: false
    },
    defaultCollapsed: {
      type: Boolean,
      default: false
    },
    showTooltips: {
      type: Boolean,
      default: true
    },
    
    // 键盘快捷键
    enableKeyboard: {
      type: Boolean,
      default: true
    },
    keyboardShortcuts: {
      type: Object,
      default: () => ({
        marker: 'M',
        polygon: 'P',
        polyline: 'L',
        circle: 'C',
        rectangle: 'R',
        clear: 'Delete',
        undo: 'Ctrl+Z',
        redo: 'Ctrl+Y'
      })
    }
  },
  
  data() {
    return {
      toolbar: null,
      activeTool: null,
      isCollapsed: this.defaultCollapsed
    };
  },
  
  computed: {
    // 计算配置选项
    toolbarOptions() {
      return {
        tools: this.tools,
        orientation: this.orientation,
        position: this.position,
        size: this.size,
        theme: this.theme,
        collapsible: this.collapsible,
        defaultCollapsed: this.defaultCollapsed,
        showTooltips: this.showTooltips,
        enableKeyboard: this.enableKeyboard,
        keyboardShortcuts: this.keyboardShortcuts,
        onToolClick: this.handleToolClick,
        onToolActivate: this.handleToolActivate,
        onToolDeactivate: this.handleToolDeactivate,
        onCollapse: this.handleCollapse
      };
    }
  },
  
  watch: {
    // 监听属性变化并更新组件
    toolbarOptions: {
      handler(newOptions) {
        if (this.toolbar) {
          this.updateToolbar(newOptions);
        }
      },
      deep: true
    }
  },
  
  mounted() {
    this.initToolbar();
  },
  
  beforeDestroy() {
    this.destroyToolbar();
  },
  
  methods: {
    /**
     * 初始化工具栏组件
     */
    initToolbar() {
      try {
        // 创建lib库的Toolbar组件
        this.toolbar = componentManager.create(
          'toolbar',
          this.$refs.toolbarContainer,
          this.toolbarOptions
        );
        
        // 注册组件到管理器
        const componentId = `toolbar-${this._uid}`;
        componentManager.register(componentId, this.toolbar);
        
        this.$emit('ready', this.toolbar);
      } catch (error) {
        console.error('Toolbar初始化失败:', error);
        this.$emit('error', error);
      }
    },
    
    /**
     * 更新工具栏配置
     */
    updateToolbar(options) {
      try {
        if (this.toolbar && this.toolbar.updateOptions) {
          this.toolbar.updateOptions(options);
        }
      } catch (error) {
        console.error('Toolbar更新失败:', error);
        this.$emit('error', error);
      }
    },
    
    /**
     * 销毁工具栏组件
     */
    destroyToolbar() {
      if (this.toolbar) {
        try {
          const componentId = `toolbar-${this._uid}`;
          componentManager.remove(componentId);
          this.toolbar = null;
        } catch (error) {
          console.error('Toolbar销毁失败:', error);
        }
      }
    },
    
    /**
     * 处理工具点击事件
     */
    handleToolClick(toolId, tool, activeTool) {
      this.$emit('tool-click', {
        toolId,
        tool,
        activeTool,
        isActive: activeTool
      });
    },
    
    /**
     * 处理工具激活事件
     */
    handleToolActivate(toolId, tool) {
      this.activeTool = toolId;
      this.$emit('tool-activate', {
        toolId,
        tool
      });
    },
    
    /**
     * 处理工具取消激活事件
     */
    handleToolDeactivate(toolId, tool) {
      this.activeTool = null;
      this.$emit('tool-deactivate', {
        toolId,
        tool
      });
    },
    
    /**
     * 处理折叠事件
     */
    handleCollapse(collapsed) {
      this.isCollapsed = collapsed;
      this.$emit('collapse', collapsed);
    },
    
    // ==================== 公共方法 ====================
    
    /**
     * 激活工具
     */
    activateTool(toolId) {
      if (this.toolbar) {
        this.toolbar.activateTool(toolId);
        this.activeTool = toolId;
      }
    },
    
    /**
     * 取消激活工具
     */
    deactivateTool() {
      if (this.toolbar) {
        this.toolbar.deactivateTool();
        this.activeTool = null;
      }
    },
    
    /**
     * 添加工具
     */
    addTool(tool, index = -1) {
      if (this.toolbar) {
        this.toolbar.addTool(tool, index);
      }
    },
    
    /**
     * 移除工具
     */
    removeTool(toolId) {
      if (this.toolbar) {
        this.toolbar.removeTool(toolId);
      }
    },
    
    /**
     * 设置工具启用状态
     */
    setToolEnabled(toolId, enabled) {
      if (this.toolbar) {
        this.toolbar.setToolEnabled(toolId, enabled);
      }
    },
    
    /**
     * 获取工具启用状态
     */
    getToolEnabled(toolId) {
      if (this.toolbar) {
        return this.toolbar.getToolEnabled(toolId);
      }
      return false;
    },
    
    /**
     * 设置工具可见性
     */
    setToolVisible(toolId, visible) {
      if (this.toolbar) {
        this.toolbar.setToolVisible(toolId, visible);
      }
    },
    
    /**
     * 获取工具可见性
     */
    getToolVisible(toolId) {
      if (this.toolbar) {
        return this.toolbar.getToolVisible(toolId);
      }
      return false;
    },
    
    /**
     * 折叠/展开工具栏
     */
    toggleCollapse() {
      if (this.toolbar) {
        this.toolbar.toggleCollapse();
      }
    },
    
    /**
     * 折叠工具栏
     */
    collapse() {
      if (this.toolbar) {
        this.toolbar.collapse();
        this.isCollapsed = true;
      }
    },
    
    /**
     * 展开工具栏
     */
    expand() {
      if (this.toolbar) {
        this.toolbar.expand();
        this.isCollapsed = false;
      }
    },
    
    /**
     * 获取当前激活的工具
     */
    getActiveTool() {
      return this.activeTool;
    },
    
    /**
     * 获取所有工具
     */
    getTools() {
      if (this.toolbar) {
        return this.toolbar.getTools();
      }
      return this.tools;
    },
    
    /**
     * 获取工具
     */
    getTool(toolId) {
      if (this.toolbar) {
        return this.toolbar.getTool(toolId);
      }
      return this.tools.find(tool => tool.id === toolId);
    },
    
    /**
     * 获取当前状态
     */
    getState() {
      return {
        activeTool: this.activeTool,
        isCollapsed: this.isCollapsed,
        tools: this.tools,
        orientation: this.orientation,
        position: this.position
      };
    },
    
    /**
     * 获取lib库组件实例
     */
    getToolbarInstance() {
      return this.toolbar;
    }
  }
};
</script>

<style scoped>
.toolbar-enhanced {
  position: relative;
  z-index: 1000;
}

/* 确保组件容器有足够的空间 */
.toolbar-enhanced >>> .toolbar-container {
  width: 100%;
  height: 100%;
}

/* 水平布局 */
.toolbar-enhanced.horizontal {
  display: flex;
  flex-direction: row;
}

/* 垂直布局 */
.toolbar-enhanced.vertical {
  display: flex;
  flex-direction: column;
}

/* 小尺寸样式 */
.toolbar-enhanced.small {
  font-size: 12px;
}

/* 普通尺寸样式 */
.toolbar-enhanced.normal {
  font-size: 14px;
}

/* 大尺寸样式 */
.toolbar-enhanced.large {
  font-size: 16px;
}

/* 深色主题样式 */
.toolbar-enhanced.dark {
  /* 深色主题相关样式将由lib库处理 */
}

/* 位置样式 */
.toolbar-enhanced.top-left {
  position: absolute;
  top: 10px;
  left: 10px;
}

.toolbar-enhanced.top-right {
  position: absolute;
  top: 10px;
  right: 10px;
}

.toolbar-enhanced.bottom-left {
  position: absolute;
  bottom: 10px;
  left: 10px;
}

.toolbar-enhanced.bottom-right {
  position: absolute;
  bottom: 10px;
  right: 10px;
}

.toolbar-enhanced.top-center {
  position: absolute;
  top: 10px;
  left: 50%;
  transform: translateX(-50%);
}

.toolbar-enhanced.bottom-center {
  position: absolute;
  bottom: 10px;
  left: 50%;
  transform: translateX(-50%);
}

.toolbar-enhanced.left-center {
  position: absolute;
  left: 10px;
  top: 50%;
  transform: translateY(-50%);
}

.toolbar-enhanced.right-center {
  position: absolute;
  right: 10px;
  top: 50%;
  transform: translateY(-50%);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .toolbar-enhanced {
    font-size: 12px;
  }
  
  .toolbar-enhanced.horizontal {
    flex-wrap: wrap;
  }
}
</style>
