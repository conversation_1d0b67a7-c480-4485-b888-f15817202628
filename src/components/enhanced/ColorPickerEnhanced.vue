<template>
  <div class="color-picker-enhanced" ref="colorPickerContainer">
    <!-- lib库的ColorPicker组件将在这里渲染 -->
  </div>
</template>

<script>
import { componentManager } from '../../../lib/components/index.js';

export default {
  name: 'ColorPickerEnhanced',
  props: {
    // 颜色值
    value: {
      type: String,
      default: '#1890ff'
    },
    defaultColor: {
      type: String,
      default: '#1890ff'
    },
    
    // 预设颜色
    presetColors: {
      type: Array,
      default: () => [
        '#1890ff', '#096dd9', '#0050b3', '#003a8c', '#002766',
        '#52c41a', '#389e0d', '#237804', '#135200', '#092b00',
        '#fa541c', '#d4380d', '#ad2102', '#871400', '#612500',
        '#eb2f96', '#c41d7f', '#9e1068', '#780650', '#520339',
        '#722ed1', '#531dab', '#391085', '#22075e', '#120338',
        '#13c2c2', '#08979c', '#006d75', '#00474f', '#002329',
        '#faad14', '#d48806', '#ad6800', '#874d00', '#613400',
        '#f5222d', '#cf1322', '#a8071a', '#820014', '#5c0011'
      ]
    },
    
    // 显示选项
    showPresets: {
      type: Boolean,
      default: true
    },
    showInput: {
      type: Boolean,
      default: true
    },
    showClear: {
      type: Boolean,
      default: true
    },
    showAlpha: {
      type: Boolean,
      default: false
    },
    
    // 样式选项
    size: {
      type: String,
      default: 'normal',
      validator: value => ['small', 'normal', 'large'].includes(value)
    },
    theme: {
      type: String,
      default: 'light',
      validator: value => ['light', 'dark'].includes(value)
    },
    disabled: {
      type: Boolean,
      default: false
    },
    
    // 格式选项
    format: {
      type: String,
      default: 'hex',
      validator: value => ['hex', 'rgb', 'hsl', 'hsv'].includes(value)
    }
  },
  
  data() {
    return {
      colorPicker: null,
      internalValue: this.value || this.defaultColor
    };
  },
  
  computed: {
    // 计算配置选项
    colorPickerOptions() {
      return {
        defaultColor: this.internalValue,
        presetColors: this.presetColors,
        showPresets: this.showPresets,
        showInput: this.showInput,
        showClear: this.showClear,
        showAlpha: this.showAlpha,
        size: this.size,
        theme: this.theme,
        disabled: this.disabled,
        format: this.format,
        onChange: this.handleColorChange,
        onClear: this.handleColorClear
      };
    }
  },
  
  watch: {
    // 监听属性变化并更新组件
    colorPickerOptions: {
      handler(newOptions) {
        if (this.colorPicker) {
          this.updateColorPicker(newOptions);
        }
      },
      deep: true
    },
    
    // 监听外部值变化
    value(newValue) {
      if (newValue !== this.internalValue) {
        this.internalValue = newValue;
        if (this.colorPicker) {
          this.colorPicker.setColor(newValue);
        }
      }
    }
  },
  
  mounted() {
    this.initColorPicker();
  },
  
  beforeDestroy() {
    this.destroyColorPicker();
  },
  
  methods: {
    /**
     * 初始化颜色选择器组件
     */
    initColorPicker() {
      try {
        // 创建lib库的ColorPicker组件
        this.colorPicker = componentManager.create(
          'colorPicker',
          this.$refs.colorPickerContainer,
          this.colorPickerOptions
        );
        
        // 注册组件到管理器
        const componentId = `color-picker-${this._uid}`;
        componentManager.register(componentId, this.colorPicker);
        
        // 设置初始颜色
        if (this.internalValue) {
          this.colorPicker.setColor(this.internalValue);
        }
        
        this.$emit('ready', this.colorPicker);
      } catch (error) {
        console.error('ColorPicker初始化失败:', error);
        this.$emit('error', error);
      }
    },
    
    /**
     * 更新颜色选择器配置
     */
    updateColorPicker(options) {
      try {
        if (this.colorPicker && this.colorPicker.updateOptions) {
          this.colorPicker.updateOptions(options);
        }
      } catch (error) {
        console.error('ColorPicker更新失败:', error);
        this.$emit('error', error);
      }
    },
    
    /**
     * 销毁颜色选择器组件
     */
    destroyColorPicker() {
      if (this.colorPicker) {
        try {
          const componentId = `color-picker-${this._uid}`;
          componentManager.remove(componentId);
          this.colorPicker = null;
        } catch (error) {
          console.error('ColorPicker销毁失败:', error);
        }
      }
    },
    
    /**
     * 处理颜色变化事件
     */
    handleColorChange(color) {
      this.internalValue = color;
      this.$emit('input', color);
      this.$emit('change', color);
    },
    
    /**
     * 处理颜色清空事件
     */
    handleColorClear() {
      this.internalValue = null;
      this.$emit('input', null);
      this.$emit('change', null);
      this.$emit('clear');
    },
    
    // ==================== 公共方法 ====================
    
    /**
     * 设置颜色
     */
    setColor(color) {
      this.internalValue = color;
      if (this.colorPicker) {
        this.colorPicker.setColor(color);
      }
    },
    
    /**
     * 获取颜色
     */
    getColor() {
      if (this.colorPicker) {
        return this.colorPicker.getColor();
      }
      return this.internalValue;
    },
    
    /**
     * 清空颜色
     */
    clearColor() {
      this.handleColorClear();
    },
    
    /**
     * 设置禁用状态
     */
    setDisabled(disabled) {
      if (this.colorPicker) {
        this.colorPicker.setDisabled(disabled);
      }
    },
    
    /**
     * 获取禁用状态
     */
    getDisabled() {
      if (this.colorPicker) {
        return this.colorPicker.getDisabled();
      }
      return this.disabled;
    },
    
    /**
     * 设置预设颜色
     */
    setPresetColors(colors) {
      if (this.colorPicker) {
        this.colorPicker.setPresetColors(colors);
      }
    },
    
    /**
     * 获取预设颜色
     */
    getPresetColors() {
      if (this.colorPicker) {
        return this.colorPicker.getPresetColors();
      }
      return this.presetColors;
    },
    
    /**
     * 设置格式
     */
    setFormat(format) {
      if (this.colorPicker) {
        this.colorPicker.setFormat(format);
      }
    },
    
    /**
     * 获取格式
     */
    getFormat() {
      if (this.colorPicker) {
        return this.colorPicker.getFormat();
      }
      return this.format;
    },
    
    /**
     * 打开颜色选择器
     */
    open() {
      if (this.colorPicker && this.colorPicker.open) {
        this.colorPicker.open();
      }
    },
    
    /**
     * 关闭颜色选择器
     */
    close() {
      if (this.colorPicker && this.colorPicker.close) {
        this.colorPicker.close();
      }
    },
    
    /**
     * 获取当前状态
     */
    getState() {
      if (this.colorPicker) {
        return {
          color: this.internalValue,
          format: this.format,
          disabled: this.disabled,
          presetColors: this.presetColors
        };
      }
      return null;
    },
    
    /**
     * 获取lib库组件实例
     */
    getColorPickerInstance() {
      return this.colorPicker;
    }
  }
};
</script>

<style scoped>
.color-picker-enhanced {
  display: inline-block;
  position: relative;
}

/* 确保组件容器有足够的空间 */
.color-picker-enhanced >>> .color-picker-container {
  width: 100%;
  height: 100%;
}

/* 小尺寸样式 */
.color-picker-enhanced.small {
  width: 24px;
  height: 24px;
}

/* 普通尺寸样式 */
.color-picker-enhanced.normal {
  width: 32px;
  height: 32px;
}

/* 大尺寸样式 */
.color-picker-enhanced.large {
  width: 40px;
  height: 40px;
}

/* 禁用状态样式 */
.color-picker-enhanced.disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* 深色主题样式 */
.color-picker-enhanced.dark {
  /* 深色主题相关样式将由lib库处理 */
}
</style>
